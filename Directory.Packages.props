<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <AspireVersion>9.0.0</AspireVersion>
    <AspnetVersion>9.0.0</AspnetVersion>
    <EfcoreVersion>9.0.0</EfcoreVersion>
    <MicrosoftExtensionsVersion>9.0.0</MicrosoftExtensionsVersion>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="Minio" Version="6.0.5" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="FluentAssertions" Version="6.12.2" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.3" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.3" />
    <PackageVersion Include="Testcontainers" Version="3.10.0" />
    <PackageVersion Include="AutoFixture" Version="4.18.1" />
    <PackageVersion Include="AutoFixture.Xunit2" Version="4.18.1" />
    <PackageVersion Include="AutoFixture.AutoMoq" Version="4.18.1" />
    <PackageVersion Include="Xunit.Gherkin.Quick" Version="4.0.0" />
    <PackageVersion Include="FirebaseAdmin" Version="2.4.0" />
    <PackageVersion Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.3" />
    <PackageVersion Include="ClosedXML" Version="0.105.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.3" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.3" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.3" />
    <PackageVersion Include="Microsoft.Extensions.Localization.Abstractions" Version="9.0.5" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="NLog" Version="5.4.0" />
    <PackageVersion Include="Microsoft.Extensions.Http.Resilience" Version="9.3.0" />
    <PackageVersion Include="Microsoft.Extensions.ServiceDiscovery" Version="9.1.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.11.2" />
    <PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.11.2" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.11.1" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.11.1" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Runtime" Version="1.11.1" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="9.0.3" />
    <PackageVersion Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageVersion Include="Aspire.Hosting.AppHost" Version="9.1.0" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="8.0.1" />
    <PackageVersion Include="coverlet.collector" Version="6.0.4" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.0.2" />
    <PackageVersion Include="FluentValidation" Version="11.11.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
    <PackageVersion Include="MediatR" Version="12.4.1" />
    <PackageVersion Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.3" />
    <PackageVersion Include="System.Linq.Dynamic.Core" Version="*******" />
    <PackageVersion Include="Microsoft.AspNetCore.Identity" Version="2.3.1" />
    <PackageVersion Include="Microsoft.Extensions.Identity.Stores" Version="9.0.3" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.3" />
    <PackageVersion Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.3" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.3" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.3" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.3" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="7.3.1" />
    <PackageVersion Include="Swashbuckle.AspNetCore.Annotations" Version="7.3.1" />
    <PackageVersion Include="AutoMapper" Version="14.0.0" />
    <PackageVersion Include="Asp.Versioning.Mvc" Version="8.1.0" />
    <PackageVersion Include="Marvin.Cache.Headers" Version="7.1.0" />
    <PackageVersion Include="Microsoft.AspNetCore.JsonPatch" Version="8.0.12" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Core" Version="2.1.38" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.12" />
    <PackageVersion Include="AspNetCoreRateLimit" Version="5.0.0" />
    <PackageVersion Include="MMLib.SwaggerForOcelot" Version="8.3.2" />
    <PackageVersion Include="Ocelot" Version="23.4.3" />
    <PackageVersion Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.2" />
    <PackageVersion Include="Dapper" Version="2.1.35" />
  </ItemGroup>
</Project>