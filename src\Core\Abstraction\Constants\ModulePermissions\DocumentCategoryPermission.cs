﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abstraction.Constants.ModulePermissions
{
    public static class DocumentCategoryPermission
    {
        public const string View = "DocumentCategory.View";
        public const string List = "DocumentCategory.List";
        public const string Create = "DocumentCategory.Create";
        public const string Edit = "DocumentCategory.Edit";
        public const string Delete = "DocumentCategory.Delete";
    }
}
