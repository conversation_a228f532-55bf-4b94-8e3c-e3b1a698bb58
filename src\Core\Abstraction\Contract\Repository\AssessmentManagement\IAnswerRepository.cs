using Abstraction.Contracts.Repository;
using Domain.Entities.AssessmentManagement;

namespace Abstraction.Contracts.Repository.AssessmentManagement
{
    /// <summary>
    /// Repository interface for Answer entity operations
    /// Inherits from IGenericRepository to provide standard CRUD operations
    /// Includes specific methods for answer business logic
    /// </summary>
    public interface IAnswerRepository : IGenericRepository
    {
        /// <summary>
        /// Gets all answers for a specific assessment response
        /// </summary>
        /// <param name="responseId">Assessment response identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Collection of answers for the specified response</returns>
        Task<IEnumerable<Answer>> GetAnswersByResponseIdAsync(int responseId, bool trackChanges = false);

        /// <summary>
        /// Gets answers with their selected options for a specific response
        /// </summary>
        /// <param name="responseId">Assessment response identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Collection of answers with options for the specified response</returns>
        Task<IEnumerable<Answer>> GetAnswersWithOptionsByResponseIdAsync(int responseId, bool trackChanges = false);

        /// <summary>
        /// Gets a specific answer by question and response
        /// </summary>
        /// <param name="questionId">Question identifier</param>
        /// <param name="responseId">Response identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Answer for the specified question and response</returns>
        Task<Answer?> GetAnswerByQuestionAndResponseAsync(int questionId, int responseId, bool trackChanges = false);

        /// <summary>
        /// Deletes multiple answers
        /// </summary>
        /// <param name="answers">Collection of answers to delete</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task DeleteRangeAsync(IEnumerable<Answer> answers);

        /// <summary>
        /// Adds multiple answers
        /// </summary>
        /// <param name="answers">Collection of answers to add</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task AddRangeAsync(IEnumerable<Answer> answers, CancellationToken cancellationToken = default);
    }
}
