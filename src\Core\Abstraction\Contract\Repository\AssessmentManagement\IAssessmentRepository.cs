using Abstraction.Contracts.Repository;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;

namespace Abstraction.Contracts.Repository.AssessmentManagement
{
    /// <summary>
    /// Repository interface for Assessment entity operations
    /// Inherits from IGenericRepository to provide standard CRUD operations
    /// Includes specific methods for assessment business logic
    /// </summary>
    public interface IAssessmentRepository : IGenericRepository
    {
        /// <summary>
        /// Gets assessments by role-based filtering
        /// Follows the same pattern as Resolution GetResolutionsByRoleAsync
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <param name="searchTerm">Search term for filtering</param>
        /// <param name="type">Assessment type filter</param>
        /// <param name="status">Assessment status filter</param>
        /// <param name="fromDate">Start date filter</param>
        /// <param name="toDate">End date filter</param>
        /// <param name="userRole">User role for filtering</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Collection of assessments filtered by role</returns>
        IQueryable<Assessment> GetAssessmentsByRoleAsync(int fundId, string? searchTerm, AssessmentType? type,
            AssessmentStatus? status, DateTime? fromDate, DateTime? toDate, string userRole, bool trackChanges = false);

        /// <summary>
        /// Gets assessment with all related data (questions, responses, attachments, history)
        /// Follows the same pattern as Resolution GetResolutionWithAllDataAsync
        /// </summary>
        /// <param name="assessmentId">Assessment identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Assessment with all related data or null</returns>
        Task<Assessment?> GetAssessmentWithDetailsAsync(int assessmentId, bool trackChanges = false);

        /// <summary>
        /// Gets assessment with comprehensive details including responses, answers, and answer options
        /// Used for detailed assessment analysis and review
        /// </summary>
        /// <param name="assessmentId">Assessment identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Assessment with comprehensive data or null</returns>
        Task<Assessment?> GetAssessmentWithComprehensiveDetailsAsync(int assessmentId, bool trackChanges = false);

        /// <summary>
        /// Gets assessment with including responses
        /// Used for detailed assessment analysis and review
        /// </summary>
        /// <param name="assessmentId">Assessment identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Assessment with comprehensive data or null</returns>
        Task<Assessment?> GetByIdWithResponseAsync(int assessmentId, bool trackChanges = false);
    }
}
