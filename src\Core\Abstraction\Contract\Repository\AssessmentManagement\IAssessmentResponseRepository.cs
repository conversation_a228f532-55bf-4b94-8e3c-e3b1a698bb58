using Abstraction.Contracts.Repository;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;

namespace Abstraction.Contracts.Repository.AssessmentManagement
{
    /// <summary>
    /// Repository interface for Assessment entity operations
    /// Inherits from IGenericRepository to provide standard CRUD operations
    /// Includes specific methods for assessment business logic
    /// </summary>
    public interface IAssessmentResponseRepository : IGenericRepository
    {
        /// <summary>
        /// Gets assessments Response By MemberId, AssessmentId
        /// </summary>
        /// <param name="MemberId">Board Member</param>
        /// <param name="AssessmentId">Assessment</param>
        Task<AssessmentResponse> GetAssessmentResponseByUserAsync(int MemberId, int AssessmentId);
    }
}
