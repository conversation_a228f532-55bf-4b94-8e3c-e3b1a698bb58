using Domain.Entities.ResolutionManagement;

namespace Abstraction.Contracts.Repository.Resolution
{
    /// <summary>
    /// Repository interface for ResolutionItemVoteComment entity operations
    /// Inherits from IGenericRepository to provide standard CRUD operations
    /// Includes specific methods for resolution item vote comment business logic
    /// </summary>
    public interface IResolutionItemVoteCommentRepository : IGenericRepository
    {
        /// <summary>
        /// Retrieves all comments associated with a specific ResolutionItemVote
        /// Gets comments for the ResolutionItem that the ResolutionItemVote is voting on
        /// Includes CreatedByUser navigation property and filters out soft-deleted comments
        /// </summary>
        /// <param name="resolutionItemVoteId">The ID of the ResolutionItemVote to get comments for</param>
        /// <param name="trackChanges">Whether to track changes for the returned entities</param>
        /// <returns>List of ResolutionItemVoteComment entities with related user data</returns>
        Task<List<ResolutionItemVoteComment>> GetCommentsByResolutionItemVoteIdAsync(int resolutionItemVoteId, bool trackChanges);
    }
}
