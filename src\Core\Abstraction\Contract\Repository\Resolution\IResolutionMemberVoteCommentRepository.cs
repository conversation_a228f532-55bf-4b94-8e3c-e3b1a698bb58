using Domain.Entities.ResolutionManagement;

namespace Abstraction.Contracts.Repository.Resolution
{
    /// <summary>
    /// Repository interface for ResolutionItem entity operations
    /// Inherits from IGenericRepository to provide standard CRUD operations
    /// Includes specific methods for resolution item business logic
    /// </summary>
    public interface IResolutionMemberVoteCommentRepository : IGenericRepository
    {
       
    }
}
