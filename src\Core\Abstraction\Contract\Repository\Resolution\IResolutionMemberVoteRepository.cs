﻿using Abstraction.Contracts.Repository;
using Domain.Entities.ResolutionManagement;
using Domain.Entities.ResolutionManagement.Enums;

namespace Abstraction.Contract.Repository.Resolution
{
    public interface IResolutionMemberVoteRepository : IGenericRepository
    {
        public Task<ResolutionMemberVote> GetByMemeberAndResolutionAsync(int resolutionId, int memberId, bool trackChanges);
        public Task<List<ResolutionMemberVote>> GetMembersByResolutionIdAsync(int resolutionId,bool trackChanges);
        public Task<bool> ResolutionMemberVoteHasRevoteRequest(int resolutionId, int memberId, bool trackChanges);
        Task<bool> MemberHasVoted(int resolutionId, int memberId, bool trackChanges);
        public Task<List<string>> GetMembersByResolutionItemIdAndVoteResultAsync(int resolutionItemId,ItemVoteResult itemVoteResult, bool trackChanges);
        Task<ResolutionMemberVote> GetByIdIncludedItemVotesAsync(int id, bool trackChanges);
        public Task<ResolutionMemberVote> GetMemberVoteIncludeResolutionAsync(int resolutionMemberVoteId, bool trackChanges);
    }
}
