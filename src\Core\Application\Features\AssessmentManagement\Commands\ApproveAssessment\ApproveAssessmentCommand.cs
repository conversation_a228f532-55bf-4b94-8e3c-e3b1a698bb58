using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Base.Dto;

namespace Application.Features.AssessmentManagement.Commands.ApproveAssessment
{
    /// <summary>
    /// Command for approving an assessment
    /// Based on User Story 2: Approve or Reject Assessment
    /// Implements ICommand interface for CQRS pattern integration with MediatR
    /// Follows the same pattern as other approval commands for consistency
    /// </summary>
    public record ApproveAssessmentCommand : BaseDto, ICommand<BaseResponse<string>>
    {
        /// <summary>
        /// Optional approval comments
        /// Arabic: تعليقات الموافقة
        /// </summary>
        public string? Comments { get; set; }
    }
}
