using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Base.Dto;

namespace Application.Features.AssessmentManagement.Commands.DistributeAssessment
{
    /// <summary>
    /// Command for distributing an approved assessment to board members
    /// Based on User Story 3: Distribute Assessment
    /// Implements ICommand interface for CQRS pattern integration with MediatR
    /// Follows the same pattern as other distribution commands for consistency
    /// </summary>
    public record DistributeAssessmentCommand : BaseDto, ICommand<BaseResponse<string>>
    {
    }
}
