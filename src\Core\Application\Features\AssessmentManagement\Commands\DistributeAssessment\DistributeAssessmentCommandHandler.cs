using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contract.Service;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Entities.AssessmentManagement;
using Abstraction.Constants;
using Domain.Entities.FundManagement;


namespace Application.Features.AssessmentManagement.Commands.DistributeAssessment
{
    /// <summary>
    /// Handler for DistributeAssessmentCommand
    /// Implements business logic for distributing approved assessments to board members
    /// Based on User Story 3: Distribute Assessment and Clean Architecture principles
    /// Follows the same pattern as other distribution command handlers for consistency
    /// </summary>
    public class DistributeAssessmentCommandHandler : BaseResponseHandler, ICommandHandler<DistributeAssessmentCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;

        #endregion

        #region Constructor
        public DistributeAssessmentCommandHandler(
            ILoggerManager logger,
            IRepositoryManager repository,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _logger = logger;
            _repository = repository;
            _localizer = localizer;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<string>> Handle(DistributeAssessmentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Distributing assessment with ID: {request.Id}");

                // Get assessment with fund information
                var assessment = await _repository.Assessment.GetByIdAsync<Assessment>(request.Id,true);
                if (assessment == null)
                {
                    return NotFound<string>(_localizer[SharedResourcesKey.AssessmentNotFound]);
                }

                // Initialize state pattern
                assessment.InitializeState();

                // Validate that assessment can be distributed
                if (!assessment.CanDistribute())
                {
                    return BusinessValidation<string>(_localizer[SharedResourcesKey.AssessmentCannotBeDistributed]);
                }


                // Get all board members for the fund
                var fund = await _repository.Funds.ViewFundUsers(assessment.FundId, false);
                var boardMemberIds = fund.BoardMembers.Select(bm => bm.Id).ToList();

                if (!boardMemberIds.Any())
                {
                    return BusinessValidation<string>(_localizer[SharedResourcesKey.NoBoardMembersFound]);
                }

                // Create assessment responses for each board member
                var assessmentResponses = new List<AssessmentResponse>();
                foreach (var boardMemberId in boardMemberIds)
                {
                    // Check if response already exists (in case of re-distribution)
                    var existingResponse = _repository.AssessmentResponse
                        .GetAssessmentResponseByUserAsync(boardMemberId, assessment.Id);

                    if (existingResponse != null)
                    {
                        assessmentResponses.Add(new AssessmentResponse
                        {
                            AssessmentId = assessment.Id,
                            BoardMemberId = boardMemberId,
                            Status = ResponseStatus.Pending
                        });
                    }
                }

                // Add new responses to database
                assessment.Responses = assessmentResponses;



                // Perform state transition with comprehensive audit
                var transitionSuccess = assessment.ChangeStatusWithAudit(
                    AssessmentStatus.Active,
                    AssessmentActionEnum.Distribution,
                    string.Empty,
                    "AssessmentDistributed",
                    _currentUserService.UserId.GetValueOrDefault(),
                    Roles.FundManager.ToString(),
                    $"Assessment '{assessment.Title}' distributed to {boardMemberIds.Count} board members by {Roles.FundManager.ToString()}"
                );

                if (!transitionSuccess)
                {
                    return BusinessValidation<string>(_localizer[SharedResourcesKey.InvalidStatusTransition]);
                }

                // Update assessment
                await _repository.Assessment.UpdateAsync(assessment);

                // Send notifications to all board members
                var boardMemberUserIds = fund.BoardMembers.Select(bm => bm.UserId).ToList();

                await AddNotification(fund, assessment, boardMemberUserIds);

                _logger.LogInfo($"Assessment distributed successfully with ID: {assessment.Id} to {boardMemberIds.Count} board members");

                return Success<string>(_localizer[SharedResourcesKey.AssessmentDistributedSuccessfully]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,$"Error distributing assessment: {ex.Message}");
                return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorSavingData]);
            }
        }

        /// <summary>
        /// Adds notifications for assessment distribution
        /// Notifies all board members when assessment is distributed for review
        /// </summary>
        private async Task AddNotification(Fund fundDetails, Assessment assessment, List<int> boardMemberIds)
        {
            var notifications = new List<Domain.Entities.Notifications.Notification>();

            // Notify all board members
            foreach (var boardMemberId in boardMemberIds)
            {
                notifications.Add(new Domain.Entities.Notifications.Notification
                {
                    Title = string.Empty,
                    Body = $"{assessment.Title}|{fundDetails.Name}|{_currentUserService.UserName}",
                    FundId = fundDetails.Id,
                    UserId = boardMemberId,
                    NotificationType = (int)Domain.Entities.Notifications.NotificationType.AssessmentDistributed,
                    NotificationModule = (int)Domain.Entities.Notifications.NotificationModule.Evaluations,
                    IsRead = false
                });
            }

            if (notifications.Any())
            {
                await _repository.Notifications.AddRangeAsync(notifications);
                _logger.LogInfo($"Assessment distribution notifications added for Assessment ID: {assessment.Id}, Count: {notifications.Count}");
            }
        }

        #endregion
    }
}
