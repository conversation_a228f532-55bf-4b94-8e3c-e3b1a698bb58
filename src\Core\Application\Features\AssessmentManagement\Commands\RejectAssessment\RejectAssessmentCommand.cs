using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Application.Features.AssessmentManagement.Dtos;

namespace Application.Features.AssessmentManagement.Commands.RejectAssessment
{
    /// <summary>
    /// Command for rejecting an assessment
    /// Based on User Story 2: Approve or Reject Assessment
    /// Implements ICommand interface for CQRS pattern integration with MediatR
    /// Follows the same pattern as other rejection commands for consistency
    /// </summary>
    public record RejectAssessmentCommand : RejectAssessmentDto, ICommand<BaseResponse<string>>
    {
        // Command inherits all properties from RejectAssessmentDto
        // No additional properties needed unless specific to command execution
    }
}
