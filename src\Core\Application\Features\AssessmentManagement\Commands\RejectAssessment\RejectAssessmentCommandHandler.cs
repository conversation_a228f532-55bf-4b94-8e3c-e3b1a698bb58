using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contract.Service;
using Domain.Entities.AssessmentManagement.Enums;
using Abstraction.Constants;

using Domain.Entities.AssessmentManagement;
using Domain.Entities.FundManagement;
using Domain.Entities.Users;

namespace Application.Features.AssessmentManagement.Commands.RejectAssessment
{
    /// <summary>
    /// Handler for RejectAssessmentCommand
    /// Implements business logic for rejecting assessments with state pattern integration
    /// Based on User Story 2: Approve or Reject Assessment and Clean Architecture principles
    /// Follows the Jadwa CQRS coding standards for consistency
    /// </summary>
    public class RejectAssessmentCommandHandler : BaseResponseHandler, ICommandHandler<RejectAssessmentCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;

        #endregion

        #region Constructor
        public RejectAssessmentCommandHandler(
            ILoggerManager logger,
            IRepositoryManager repository,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _logger = logger;
            _repository = repository;
            _localizer = localizer;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<string>> Handle(RejectAssessmentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Starting assessment rejection for ID: {request.Id}");

                // 1. Validate current user
                var currentUserId = _currentUserService.UserId;
                if (!currentUserId.HasValue)
                {
                    _logger.LogWarn("Current user ID is null during assessment rejection");
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 2. Get assessment with fund details
                var assessment = await _repository.Assessment.GetByIdAsync<Assessment>(request.Id, trackChanges: true);
                if (assessment == null)
                {
                    _logger.LogWarn($"Assessment not found with ID: {request.Id}");
                    return NotFound<string>(_localizer[SharedResourcesKey.AssessmentNotFound]);
                }

                // 3. Get fund information for role validation
                var fund = await _repository.Funds.ViewFundUsers(assessment.FundId, trackChanges: false);
                if (fund == null)
                {
                    _logger.LogWarn($"Fund not found with ID: {assessment.FundId}");
                    return NotFound<string>(_localizer[SharedResourcesKey.FundNotFound]);
                }

                // 4. Get user's fund-specific role (following coding standards)
                var userRole = await GetUserFundRole(fund, currentUserId.Value);
                if (userRole == Roles.None)
                {
                    _logger.LogWarn($"User {currentUserId.Value} has no valid role in fund {assessment.FundId}");
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 5. Get current user information for notifications
                var currentUser = await _repository.User.GetByIdAsync<User>(currentUserId.Value, trackChanges: false);
                var userName = currentUser?.FullName ?? currentUser?.UserName ?? "Unknown User";

                // 6. Initialize state pattern
                assessment.InitializeState();

                // 7. Validate that assessment can be rejected
                if (!assessment.CanReject())
                {
                    _logger.LogWarn($"Assessment {request.Id} cannot be rejected in current status: {assessment.Status}");
                    return BadRequest<string>(_localizer[SharedResourcesKey.AssessmentCannotBeRejected]);
                }

                // 8. Perform state transition with comprehensive audit
                var transitionSuccess = assessment.ChangeStatusWithAudit(
                    AssessmentStatus.Rejected,
                    AssessmentActionEnum.Rejection,
                    request.Comments ?? "Assessment rejected",
                    "AssessmentRejected",
                    currentUserId.Value,
                    userRole.ToString(),
                    $"Assessment '{assessment.Title}' rejected by {userRole}",
                    request.RejectionReason
                );

                if (!transitionSuccess)
                {
                    _logger.LogWarn($"Failed to transition assessment {request.Id} to Rejected status");
                    return BadRequest<string>(_localizer[SharedResourcesKey.InvalidStatusTransition]);
                }

                // 10. Update assessment metadata
                assessment.UpdatedBy = currentUserId.Value;
                assessment.UpdatedAt = DateTime.Now;

                // 11. Save changes to database (CRITICAL FIX: This was missing!)
                var updateResult = await _repository.Assessment.UpdateAsync(assessment);
                if (updateResult == null)
                {
                    _logger.LogError(null, $"Failed to update assessment with ID: {request.Id}");
                    return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorSavingData]);
                }

                // 11.5. Send notification to Fund Manager (assessment creator) with rejection reason
                await AddNotification(fund, assessment, request.RejectionReason);

                // 12. Verify status was persisted correctly
                _logger.LogInfo($"Assessment status after transition: {assessment.Status}");
                _logger.LogInfo($"Assessment rejected successfully with ID: {assessment.Id} by user: {userName} (Role: {userRole})");

                return Success<string>(_localizer[SharedResourcesKey.AssessmentRejectedSuccessfully]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error rejecting assessment with ID: {request.Id}");
                return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorSavingData]);
            }
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Determines the current user's role within a specific fund context
        /// Checks FundBoardSecretary table, Fund.LegalCouncilId field, and FundManager table
        /// Follows Clean Architecture and CQRS patterns with proper repository usage
        /// </summary>
        /// <param name="fundDetails">The fund details to check roles for</param>
        /// <param name="currentUserId">The current user ID to check roles for</param>
        /// <returns>User's role in the fund context</returns>
        private async Task<Roles> GetUserFundRole(Fund fundDetails, int currentUserId)
        {
            try
            {
                _logger.LogInfo($"Checking fund roles for User ID: {currentUserId} in Fund ID: {fundDetails.Id}");

                var userRole = Roles.None;

                // 1. Check if user is Legal Council for the fund
                if (fundDetails.LegalCouncilId == currentUserId)
                {
                    userRole = Roles.LegalCouncil;
                    _logger.LogInfo($"User ID: {currentUserId} is Legal Council for Fund ID: {fundDetails.Id}");
                }

                // 2. Check if user is Board Secretary for the fund
                if (fundDetails.FundBoardSecretaries != null && fundDetails.FundBoardSecretaries.Count > 0)
                {
                    var isBoardSecretary = fundDetails.FundBoardSecretaries.Any(bs => bs.UserId == currentUserId);
                    if (isBoardSecretary)
                    {
                        userRole = Roles.BoardSecretary;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Secretary for Fund ID: {fundDetails.Id}");
                    }
                }

                // 3. Check if user is Fund Manager for the fund
                if (fundDetails.FundManagers != null && fundDetails.FundManagers.Count > 0)
                {
                    var isFundManager = fundDetails.FundManagers.Any(fm => fm.UserId == currentUserId);
                    if (isFundManager)
                    {
                        userRole = Roles.FundManager;
                        _logger.LogInfo($"User ID: {currentUserId} is Fund Manager for Fund ID: {fundDetails.Id}");
                    }
                }

                // 4. Check if user is Board Member for the fund
                if (fundDetails.BoardMembers != null && fundDetails.BoardMembers.Count > 0)
                {
                    var isBoardMember = fundDetails.BoardMembers.Any(bm => bm.UserId == currentUserId);
                    if (isBoardMember)
                    {
                        userRole = Roles.BoardMember;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Member for Fund ID: {fundDetails.Id}");
                    }
                }

                _logger.LogInfo($"User ID: {currentUserId} has role in Fund ID: {fundDetails.Id}: '{userRole}'");
                return userRole;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking fund roles for User ID: {currentUserId} in Fund ID: {fundDetails.Id}");
                return Roles.None;
            }
        }

        /// <summary>
        /// Adds notification for assessment rejection
        /// Notifies the Fund Manager (assessment creator) when assessment is rejected with reason
        /// </summary>
        private async Task AddNotification(Fund fundDetails, Assessment assessment, string rejectionReason)
        {
            var notifications = new List<Domain.Entities.Notifications.Notification>();

            // Notify the Fund Manager who created the assessment
            
            notifications.Add(new Domain.Entities.Notifications.Notification
            {
                Title = string.Empty,
                Body = $"{assessment.Title}|{fundDetails.Name}|{_currentUserService.UserName}|{rejectionReason}",
                FundId = fundDetails.Id,
                UserId = assessment.CreatedBy,
                NotificationType = (int)Domain.Entities.Notifications.NotificationType.AssessmentRejected,
                NotificationModule = (int)Domain.Entities.Notifications.NotificationModule.Evaluations,
                IsRead = false
            });

            if (notifications.Any())
            {
                await _repository.Notifications.AddRangeAsync(notifications);
                _logger.LogInfo($"Assessment rejection notification added for Assessment ID: {assessment.Id}, Count: {notifications.Count}");
            }
        }

        #endregion
    }
}
