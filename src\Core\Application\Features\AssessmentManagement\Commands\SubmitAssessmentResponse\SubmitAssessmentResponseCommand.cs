using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Application.Features.AssessmentManagement.Dtos;

namespace Application.Features.AssessmentManagement.Commands.SubmitAssessmentResponse
{
    /// <summary>
    /// Command for submitting assessment response by board members
    /// Based on User Story 4: Respond to Assessment
    /// Implements ICommand interface for CQRS pattern integration with MediatR
    /// Follows the same pattern as other submission commands for consistency
    /// </summary>
    public record SubmitAssessmentResponseCommand : SubmitAssessmentResponseDto, ICommand<BaseResponse<string>>
    {
        // Command inherits all properties from SubmitAssessmentResponseDto
        // No additional properties needed unless specific to command execution
    }
}
