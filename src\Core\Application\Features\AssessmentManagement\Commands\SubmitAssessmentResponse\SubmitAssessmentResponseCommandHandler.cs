using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contract.Service;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Entities.AssessmentManagement;
using Abstraction.Constants;

using Domain.Entities.FundManagement;
using Domain.Helpers;

namespace Application.Features.AssessmentManagement.Commands.SubmitAssessmentResponse
{
    /// <summary>
    /// Handler for SubmitAssessmentResponseCommand
    /// Implements business logic for board members submitting assessment responses
    /// Based on User Story 4: Respond to Assessment and Clean Architecture principles
    /// Follows the same pattern as other submission command handlers for consistency
    /// </summary>
    public class SubmitAssessmentResponseCommandHandler : BaseResponseHandler, ICommandHandler<SubmitAssessmentResponseCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;

        #endregion

        #region Constructor
        public SubmitAssessmentResponseCommandHandler(
            ILoggerManager logger,
            IRepositoryManager repository,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _logger = logger;
            _repository = repository;
            _localizer = localizer;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<string>> Handle(SubmitAssessmentResponseCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Submitting response for assessment ID: {request.AssessmentId} by user: {_currentUserService.UserId}");

                // Get assessment with questions
                var assessment = await _repository.Assessment.GetByIdWithResponseAsync(request.AssessmentId,true);
                if (assessment == null)
                {
                    return NotFound<string>(_localizer[SharedResourcesKey.AssessmentNotFound]);
                }

                // Initialize state pattern
                assessment.InitializeState();

                // Validate that assessment can receive responses
                if (!assessment.CanReceiveResponses())
                {
                    return BusinessValidation<string>(_localizer[SharedResourcesKey.AssessmentCannotReceiveResponses]);
                }

                // Get current user's board member record
                var boardMember = await _repository.BoardMembers.GetBoardMemberByUserId(_currentUserService.UserId.Value,assessment.FundId);
                if (boardMember == null)
                {
                    return BusinessValidation<string>(_localizer[SharedResourcesKey.UserNotBoardMember]);
                }

                // Check if response already exists
                var assessmentResponse = assessment.Responses.FirstOrDefault(re=>re.BoardMemberId == boardMember.Id);

                // Check if response is already completed and not allowing edits
                if (assessmentResponse.Status == ResponseStatus.Completed && !assessment.AllowResponseEditing)
                {
                    return BusinessValidation<string>(_localizer[SharedResourcesKey.ResponseAlreadySubmitted]);
                }
            
                // Update response properties
                assessmentResponse.Status = request.SaveAsDraft ? ResponseStatus.Pending : ResponseStatus.Completed;

                // Handle answers for questionnaire type assessments
                if (assessment.Type == AssessmentType.Questionnaire && request.Answers?.Any() == true)
                {
                    // Remove existing answers for this response
                    var existingAnswers = await _repository.Answer
                        .GetAnswersByResponseIdAsync(assessmentResponse.Id);

                    if (existingAnswers.Any())
                    {
                        await _repository.Answer.DeleteRangeAsync(existingAnswers);
                    }

                    // Add new answers
                    foreach (var answerDto in request.Answers)
                    {
                        var answer = new Answer
                        {
                            ResponseId = assessmentResponse.Id,
                            QuestionId = answerDto.QuestionId,
                            AnswerValue = answerDto.TextAnswer,
                        };

                        // Handle selected options for choice-type questions
                        if (answerDto.SelectedOptionIds?.Any() == true)
                        {
                            answer.SelectedOptions = answerDto.SelectedOptionIds.Select(optionId => new AnswerOption
                            {
                                OptionId = optionId,
                            }).ToList();
                        }

                        assessmentResponse.Answers.Add(answer);
                    }

                    //await _repository.Answer.AddRangeAsync(answers);
                }
                // Update assessment response
                //await _repository.AssessmentResponse.UpdateAsync(assessmentResponse);

                // Send notification to Fund Manager if response is submitted (not saved as draft)
                if (!request.SaveAsDraft)
                {
                    var fundDetails = await _repository.Funds.GetByIdAsync<Fund>(assessment.FundId, false);
                    await AddNotification(fundDetails, assessment);
                }

                // Check if assessment should be marked as completed and send notifications
                if (!request.SaveAsDraft)
                {
                    await CheckAndCompleteAssessmentIfNeeded(assessment);
                }
                await _repository.Assessment.UpdateAsync(assessment);

                _logger.LogInfo($"Assessment response submitted successfully for assessment ID: {request.AssessmentId}");

                var successMessage = request.SaveAsDraft 
                    ? _localizer[SharedResourcesKey.AssessmentResponseSavedAsDraft]
                    : _localizer[SharedResourcesKey.AssessmentResponseSubmittedSuccessfully];

                return Success<string>(successMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,$"Error submitting assessment response: {ex.Message}");
                return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorSavingData]);
            }
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Checks if all responses are completed and marks assessment as completed if needed
        /// </summary>
        /// <param name="assessment">Assessment to check</param>
        /// <param name="currentResponse">The response that was just submitted</param>
        private async Task CheckAndCompleteAssessmentIfNeeded(Assessment assessment)
        {
            try
            {

                // Check if all responses are completed
                var allResponses = assessment.Responses?.ToList() ?? new List<AssessmentResponse>();
                var completedResponses = allResponses.Count(r => r.Status == ResponseStatus.Completed);
                var totalResponses = allResponses.Count;

                _logger.LogInfo($"Assessment {assessment.Id} completion status: {completedResponses}/{totalResponses} responses completed");

                // If all responses are completed, mark assessment as completed
                if (totalResponses > 0 && completedResponses == totalResponses)
                {
                    // Update assessment status to completed
                    assessment.Status = AssessmentStatus.Completed;
                    var transitionSuccess = assessment.ChangeStatusWithAudit(
                    AssessmentStatus.Completed,
                    AssessmentActionEnum.Completion,
                    "Assessment Completed",
                    "AssessmentCompleted",
                    _currentUserService.UserId.Value,
                    "",
                    $"Assessment '{assessment.Title}'"
                );
                    await AddNotificationInComplete(assessment.Fund, assessment);
                    _logger.LogInfo($"Assessment {assessment.Id} marked as completed - all {totalResponses} responses received");
                }

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking assessment completion: {ex.Message}");
                // Don't throw - this shouldn't break the main response submission flow
            }
        }

        /// <summary>
        /// Adds notification for assessment response submission
        /// Notifies the Fund Manager (assessment creator) when a board member submits response
        /// </summary>
        private async Task AddNotification(Fund fundDetails, Assessment assessment)
        {
            var notifications = new List<Domain.Entities.Notifications.Notification>();

            // Notify the Fund Manager who created the assessment
            
            notifications.Add(new Domain.Entities.Notifications.Notification
            {
                Title = string.Empty,
                Body = $"{_currentUserService.UserName}|{assessment.Title}|{fundDetails.Name}",
                FundId = fundDetails.Id,
                UserId = assessment.CreatedBy,
                NotificationType = (int)Domain.Entities.Notifications.NotificationType.AssessmentResponseSubmitted,
                NotificationModule = (int)Domain.Entities.Notifications.NotificationModule.Evaluations,
                IsRead = false
            });

            if (notifications.Any())
            {
                await _repository.Notifications.AddRangeAsync(notifications);
                _logger.LogInfo($"Assessment response submission notification added for Assessment ID: {assessment.Id}, Count: {notifications.Count}");
            }
        }

        /// <summary>
        /// Adds notification for assessment completion
        /// Notifies the Fund Manager (assessment creator) when the assessment completed
        /// </summary>
        private async Task AddNotificationInComplete(Fund fundDetails, Assessment assessment)
        {
            var notifications = new List<Domain.Entities.Notifications.Notification>();

            // Notify the Fund Manager who created the assessment

            notifications.Add(new Domain.Entities.Notifications.Notification
            {
                Title = string.Empty,
                Body = $"{assessment.Title}|{fundDetails.Name}",
                FundId = fundDetails.Id,
                UserId = assessment.CreatedBy,
                NotificationType = (int)Domain.Entities.Notifications.NotificationType.AssessmentCompleted,
                NotificationModule = (int)Domain.Entities.Notifications.NotificationModule.Evaluations,
                IsRead = false
            });

            if (notifications.Any())
            {
                await _repository.Notifications.AddRangeAsync(notifications);
                _logger.LogInfo($"Assessment completion notification added for Assessment ID: {assessment.Id}, Count: {notifications.Count}");
            }
        }

        #endregion
    }
}
