using Abstraction.Base.Dto;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Assessment Answer Option
    /// Arabic: كائل نقل البيانات لخيار إجابة التقييم
    /// </summary>
    public record AssessmentAnswerOptionDto : BaseDto
    {
        /// <summary>
        /// Option ID that was selected
        /// Arabic: معرف الخيار المختار
        /// </summary>
        public int OptionId { get; set; }

        /// <summary>
        /// Option text for display
        /// Arabic: نص الخيار للعرض
        /// </summary>
        public string? OptionText { get; set; }

    }
}
