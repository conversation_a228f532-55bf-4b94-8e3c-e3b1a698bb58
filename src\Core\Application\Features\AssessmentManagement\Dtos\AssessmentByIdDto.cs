using Abstraction.Base.Dto;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Entities.AssessmentManagement;
using Application.Features.Resolutions.Dtos;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Assessment details by ID endpoint
    /// Contains specific fields required for the GET /api/assessments/{id} endpoint
    /// Based on API specification requirements
    /// Arabic: كائل نقل البيانات لتفاصيل التقييم حسب المعرف
    /// </summary>
    public record AssessmentByIdDto : BaseDto
    {
        /// <summary>
        /// Fund information
        /// Arabic: معلومات الصندوق
        /// </summary>
        public int FundId { get; set; }

        /// <summary>
        /// Fund name
        /// Arabic: اسم الصندوق
        /// </summary>
        public string FundName { get; set; } = string.Empty;

        /// <summary>
        /// Assessment title
        /// Arabic: عنوان التقييم
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Assessment description
        /// Arabic: وصف التقييم
        /// </summary>
        public string? Description { get; set; }



        /// <summary>
        /// Assessment instructions
        /// Arabic: تعليمات التقييم
        /// </summary>
        public string? Instructions { get; set; }

        /// <summary>
        /// Assessment type (Questionnaire or Attachment)
        /// Arabic: نوع التقييم
        /// </summary>
        public AssessmentType Type { get; set; }

        /// <summary>
        /// Questions list (for Questionnaire type assessments)
        /// Arabic: قائمة الأسئلة
        /// </summary>
        public List<AssessmentQuestionByIdDto>? Questions { get; set; }

        /// <summary>
        /// Attachment details (for Attachment type assessments)
        /// Arabic: تفاصيل المرفق
        /// </summary>
        public AttachmentDto? Attachment { get; set; }

        /// <summary>
        /// Assessment status history for audit trail
        /// Arabic: تاريخ حالة التقييم
        /// </summary>
        public List<AssessmentStatusHistoryByIdDto>? StatusHistory { get; set; }

        // Permission Properties - Based on user role and assessment status
        /// <summary>
        /// Indicates if current user can edit the assessment
        /// Based on user role and assessment status per business requirements
        /// Arabic: يمكن للمستخدم الحالي تعديل التقييم
        /// </summary>
        public bool CanEdit { get; set; }

        /// <summary>
        /// Indicates if current user can delete the assessment
        /// Based on user role and assessment status per business requirements
        /// Arabic: يمكن للمستخدم الحالي حذف التقييم
        /// </summary>
        public bool CanDelete { get; set; }

        /// <summary>
        /// Indicates if current user can approve the assessment
        /// Only Legal Council and Board Secretary can approve assessments waiting for approval
        /// Arabic: يمكن للمستخدم الحالي الموافقة على التقييم
        /// </summary>
        public bool CanApprove { get; set; }

        /// <summary>
        /// Indicates if current user can reject the assessment
        /// Only Legal Council and Board Secretary can reject assessments waiting for approval
        /// Arabic: يمكن للمستخدم الحالي رفض التقييم
        /// </summary>
        public bool CanReject { get; set; }

        /// <summary>
        /// Indicates if current user can distribute the assessment
        /// Only Fund Managers can distribute approved assessments
        /// Arabic: يمكن للمستخدم الحالي توزيع التقييم
        /// </summary>
        public bool CanDistribute { get; set; }

        /// <summary>
        /// Indicates if current user can answer/respond to the assessment
        /// Only Board Members can respond to active assessments they haven't already answered
        /// Arabic: يمكن للمستخدم الحالي الإجابة على التقييم
        /// </summary>
        public bool CanAnswer { get; set; }

        /// <summary>
        /// Current status of the assessment
        /// </summary>
        public AssessmentStatus Status { get; set; }
    }

    /// <summary>
    /// DTO for Assessment Question in the by ID response
    /// Arabic: كائل نقل البيانات لسؤال التقييم في الاستجابة حسب المعرف
    /// </summary>
    public record AssessmentQuestionByIdDto : BaseDto
    {
        /// <summary>
        /// Question text
        /// Arabic: نص السؤال
        /// </summary>
        public string QuestionText { get; set; } = string.Empty;

        /// <summary>
        /// Question type
        /// Arabic: نوع السؤال
        /// </summary>
        public QuestionType QuestionType { get; set; }

        /// <summary>
        /// Question display order
        /// Arabic: ترتيب عرض السؤال
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Whether the question is required
        /// Arabic: السؤال مطلوب
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// Question options (for choice-type questions)
        /// Arabic: خيارات السؤال
        /// </summary>
        public List<AssessmentOptionByIdDto>? Options { get; set; }
    }

    /// <summary>
    /// DTO for Assessment Option in the by ID response
    /// Arabic: كائل نقل البيانات لخيار التقييم في الاستجابة حسب المعرف
    /// </summary>
    public record AssessmentOptionByIdDto : BaseDto
    {
        /// <summary>
        /// Option text/value
        /// Arabic: نص الخيار
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// Option display order
        /// Arabic: ترتيب عرض الخيار
        /// </summary>
        public int Order { get; set; }
    }

    /// <summary>
    /// DTO for Assessment Attachment in the by ID response
    /// Arabic: كائل نقل البيانات لمرفق التقييم في الاستجابة حسب المعرف
    /// </summary>
    public record AssessmentAttachmentByIdDto : BaseDto
    {
        /// <summary>
        /// Original file name
        /// Arabic: اسم الملف الأصلي
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// File size in bytes
        /// Arabic: حجم الملف بالبايت
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// File content type (MIME type)
        /// Arabic: نوع محتوى الملف
        /// </summary>
        public string ContentType { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for Assessment Status History in the by ID response
    /// Arabic: كائل نقل البيانات لتاريخ حالة التقييم في الاستجابة حسب المعرف
    /// </summary>
    public record AssessmentStatusHistoryByIdDto : BaseDto
    {
        /// <summary>
        /// Assessment status
        /// Arabic: حالة التقييم
        /// </summary>
        public AssessmentStatus Status { get; set; }

        /// <summary>
        /// Status display name (localized)
        /// Arabic: اسم الحالة للعرض
        /// </summary>
        public string StatusDisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Action performed
        /// Arabic: الإجراء المنفذ
        /// </summary>
        public AssessmentActionEnum Action { get; set; }

        /// <summary>
        /// Action display name (localized)
        /// Arabic: اسم الإجراء للعرض
        /// </summary>
        public string ActionDisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Reason for the action/transition
        /// Arabic: سبب الإجراء
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// Rejection reason (specific for rejection actions)
        /// Arabic: سبب الرفض
        /// </summary>
        public string? RejectionReason { get; set; }

        /// <summary>
        /// User who performed the action
        /// Arabic: المستخدم الذي نفذ الإجراء
        /// </summary>
        public string? CreatedByName { get; set; }

        /// <summary>
        /// User role when performing the action
        /// Arabic: دور المستخدم عند تنفيذ الإجراء
        /// </summary>
        public string? UserRole { get; set; }

        /// <summary>
        /// Date and time when the action was performed
        /// Arabic: تاريخ ووقت تنفيذ الإجراء
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }
}
