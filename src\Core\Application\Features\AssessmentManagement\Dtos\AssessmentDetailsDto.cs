using Application.Features.Resolutions.Dtos;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for detailed Assessment view
    /// Contains comprehensive assessment information including questions, attachments, and responses
    /// Based on AssessmentStories.md requirements and Clean Architecture principles
    /// Follows the same pattern as ResolutionDetailsDto for consistency
    /// </summary>
    public record AssessmentDetailsDto : AssessmentDto
    {
        /// <summary>
        /// Assessment questions (for Questionnaire type)
        /// Arabic: أسئلة التقييم
        /// </summary>
        public List<CreateAssessmentQuestionDto>? Questions { get; set; }

        /// <summary>
        /// Assessment attachments (for Attachment type or supporting documents)
        /// Arabic: مرفقات التقييم
        /// </summary>
        public AttachmentDto Attachment { get; set; }
        /// <summary>
        /// Assessment status history for audit trail
        /// Arabic: تاريخ حالة التقييم
        /// </summary>
        public List<AssessmentStatusHistoryDto>? StatusHistory { get; set; }

        /// <summary>
        /// Assessment responses (for management roles)
        /// Arabic: ردود التقييم
        /// </summary>
        public List<AssessmentResponseDto>? Responses { get; set; }

        /// <summary>
        /// Board members assigned to this assessment
        /// Arabic: أعضاء مجلس الإدارة المكلفون بهذا التقييم
        /// </summary>
        public List<AssignedBoardMemberDto>? AssignedBoardMembers { get; set; }

        /// <summary>
        /// Assessment statistics
        /// Arabic: إحصائيات التقييم
        /// </summary>
        public AssessmentStatisticsDto? Statistics { get; set; }

        /// <summary>
        /// Allowed state transitions from current status
        /// Arabic: التحولات المسموحة من الحالة الحالية
        /// </summary>
        public List<AssessmentStatus> AllowedTransitions { get; set; } = new();

        /// <summary>
        /// State validation messages
        /// Arabic: رسائل التحقق من الحالة
        /// </summary>
        public List<string> ValidationMessages { get; set; } = new();

        /// <summary>
        /// Whether the current user can edit this assessment
        /// Arabic: يمكن للمستخدم الحالي تعديل هذا التقييم
        /// </summary>
        public bool CanEdit { get; set; }

        /// <summary>
        /// Whether the current user can delete this assessment
        /// Arabic: يمكن للمستخدم الحالي حذف هذا التقييم
        /// </summary>
        public bool CanDelete { get; set; }

        
        /// <summary>
        /// Whether the current user can approve this assessment
        /// Arabic: يمكن للمستخدم الحالي الموافقة على هذا التقييم
        /// </summary>
        public bool CanApprove { get; set; }

        /// <summary>
        /// Whether the current user can reject this assessment
        /// Arabic: يمكن للمستخدم الحالي رفض هذا التقييم
        /// </summary>
        public bool CanReject { get; set; }

        /// <summary>
        /// Whether the current user can distribute this assessment
        /// Arabic: يمكن للمستخدم الحالي توزيع هذا التقييم
        /// </summary>
        public bool CanDistribute { get; set; }


        /// <summary>
        /// Rejection reason (if status is Rejected)
        /// Arabic: سبب الرفض
        /// </summary>
        public string? RejectionReason { get; set; }

        /// <summary>
        /// Distribution date (when assessment was distributed to board members)
        /// Arabic: تاريخ التوزيع
        /// </summary>
        public DateTime? DistributionDate { get; set; }

        /// <summary>
        /// Completion date (when all responses were collected)
        /// Arabic: تاريخ الإكمال
        /// </summary>
        public DateTime? CompletionDate { get; set; }

        /// <summary>
        /// Last response date
        /// Arabic: تاريخ آخر رد
        /// </summary>
        public DateTime? LastResponseDate { get; set; }

        /// <summary>
        /// Average response time in hours
        /// Arabic: متوسط وقت الاستجابة بالساعات
        /// </summary>
        public double? AverageResponseTime { get; set; }
    }

    /// <summary>
    /// DTO for Assessment Status History
    /// Arabic: كائل نقل البيانات لتاريخ حالة التقييم
    /// </summary>
    public record AssessmentStatusHistoryDto
    {
        /// <summary>
        /// Status history ID
        /// Arabic: معرف تاريخ الحالة
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Assessment status
        /// Arabic: حالة التقييم
        /// </summary>
        public AssessmentStatus Status { get; set; }

        /// <summary>
        /// Status display name
        /// Arabic: اسم الحالة للعرض
        /// </summary>
        public string DisplayedStatus { get; set; } = string.Empty;

        /// <summary>
        /// Action performed
        /// Arabic: الإجراء المنفذ
        /// </summary>
        public AssessmentActionEnum Action { get; set; }

        /// <summary>
        /// Action display name
        /// Arabic: اسم الإجراء للعرض
        /// </summary>
        public string DisplayedAction { get; set; } = string.Empty;

        /// <summary>
        /// Reason for the status change
        /// Arabic: سبب تغيير الحالة
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// Rejection reason (if applicable)
        /// Arabic: سبب الرفض
        /// </summary>
        public string? RejectionReason { get; set; }

        /// <summary>
        /// Action details
        /// Arabic: تفاصيل الإجراء
        /// </summary>
        public string? ActionDetails { get; set; }

        /// <summary>
        /// User role who performed the action
        /// Arabic: دور المستخدم الذي نفذ الإجراء
        /// </summary>
        public string? UserRole { get; set; }
        /// <summary>
        /// Resolution status information with localization
        /// </summary>
        public string DisplayedUserRole { get; set; }


        /// <summary>
        /// User name who performed the action
        /// Arabic: اسم المستخدم الذي نفذ الإجراء
        /// </summary>
        public string? UserName { get; set; }
        /// <summary>
        /// User full name who made the change
        /// </summary>
        public string FullName { get; set; } = string.Empty;



        /// <summary>
        /// Date and time of the status change
        /// Arabic: تاريخ ووقت تغيير الحالة
        /// </summary>
        public DateTime CreatedAt { get; set; }
        /// <summary>
        /// User who made the status change
        /// </summary>
        public int CreatedBy { get; set; }

    }

    /// <summary>
    /// DTO for Assessment Response Summary
    /// Arabic: كائل نقل البيانات لملخص رد التقييم
    /// </summary>
    public record AssessmentResponseSummaryDto
    {
        /// <summary>
        /// Response ID
        /// Arabic: معرف الرد
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Board member user ID
        /// Arabic: معرف المستخدم لعضو مجلس الإدارة
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Board member name
        /// Arabic: اسم عضو مجلس الإدارة
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// Response status
        /// Arabic: حالة الرد
        /// </summary>
        public ResponseStatus Status { get; set; }

        /// <summary>
        /// Response status display name
        /// Arabic: اسم حالة الرد للعرض
        /// </summary>
        public string StatusDisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Response submission date
        /// Arabic: تاريخ إرسال الرد
        /// </summary>
        public DateTime? SubmittedAt { get; set; }

        /// <summary>
        /// Response completion percentage
        /// Arabic: نسبة إكمال الرد
        /// </summary>
        public decimal CompletionPercentage { get; set; }

        /// <summary>
        /// Number of answered questions
        /// Arabic: عدد الأسئلة المجاب عليها
        /// </summary>
        public int AnsweredQuestions { get; set; }

        /// <summary>
        /// Total number of questions
        /// Arabic: العدد الإجمالي للأسئلة
        /// </summary>
        public int TotalQuestions { get; set; }
    }

    /// <summary>
    /// DTO for Assigned Board Member
    /// Arabic: كائل نقل البيانات لعضو مجلس الإدارة المكلف
    /// </summary>
    public record AssignedBoardMemberDto
    {
        /// <summary>
        /// Board member user ID
        /// Arabic: معرف المستخدم لعضو مجلس الإدارة
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Board member name
        /// Arabic: اسم عضو مجلس الإدارة
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// Board member email
        /// Arabic: بريد عضو مجلس الإدارة الإلكتروني
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// Whether the board member has responded
        /// Arabic: هل رد عضو مجلس الإدارة
        /// </summary>
        public bool HasResponded { get; set; }

        /// <summary>
        /// Response status
        /// Arabic: حالة الرد
        /// </summary>
        public ResponseStatus? ResponseStatus { get; set; }

        /// <summary>
        /// Assignment date
        /// Arabic: تاريخ التكليف
        /// </summary>
        public DateTime AssignedAt { get; set; }

        /// <summary>
        /// Response submission date
        /// Arabic: تاريخ إرسال الرد
        /// </summary>
        public DateTime? RespondedAt { get; set; }
    }
}
