using Abstraction.Base.Dto;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Assessment list display
    /// Contains essential information for assessment listing and filtering
    /// Based on AssessmentStories.md requirements and Clean Architecture principles
    /// Follows the same pattern as ResolutionListDto for consistency
    /// </summary>
    public record AssessmentListDto : BaseDto
    {
        /// <summary>
        /// Assessment title
        /// Arabic: عنوان التقييم
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Assessment description (truncated for list view)
        /// Arabic: وصف التقييم
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Assessment type
        /// Arabic: نوع التقييم
        /// </summary>
        public AssessmentType Type { get; set; }

        /// <summary>
        /// Assessment type display name
        /// Arabic: اسم نوع التقييم للعرض
        /// </summary>
        public string TypeDisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Assessment status
        /// Arabic: حالة التقييم
        /// </summary>
        public AssessmentStatus Status { get; set; }

        /// <summary>
        /// Assessment status display name
        /// Arabic: اسم الحالة للعرض
        /// </summary>
        public string StatusDisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Fund ID this assessment belongs to
        /// Arabic: معرف الصندوق
        /// </summary>
        public int FundId { get; set; }

        /// <summary>
        /// Fund name for display
        /// Arabic: اسم الصندوق
        /// </summary>
        public string FundName { get; set; } = string.Empty;



        /// <summary>
        /// Number of questions (for Questionnaire type)
        /// Arabic: عدد الأسئلة
        /// </summary>
        public int QuestionCount { get; set; }

        /// <summary>
        /// Number of attachments
        /// Arabic: عدد المرفقات
        /// </summary>
        public int AttachmentCount { get; set; }

        /// <summary>
        /// Total number of expected responses
        /// Arabic: العدد الإجمالي للردود المتوقعة
        /// </summary>
        public int TotalExpectedResponses { get; set; }

        /// <summary>
        /// Number of completed responses
        /// Arabic: عدد الردود المكتملة
        /// </summary>
        public int CompletedResponses { get; set; }

        /// <summary>
        /// Number of pending responses
        /// Arabic: عدد الردود المعلقة
        /// </summary>
        public int PendingResponses { get; set; }

        /// <summary>
        /// Response completion percentage
        /// Arabic: نسبة إكمال الردود
        /// </summary>
        public decimal CompletionPercentage { get; set; }

        /// <summary>
        /// Created by user name
        /// Arabic: اسم المنشئ
        /// </summary>
        public string CreatedByName { get; set; } = string.Empty;

        /// <summary>
        /// Creation date
        /// Arabic: تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last updated date
        /// Arabic: تاريخ آخر تحديث
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Whether the current user can edit this assessment
        /// Arabic: يمكن للمستخدم الحالي تعديل هذا التقييم
        /// </summary>
        public bool CanEdit { get; set; }

        /// <summary>
        /// Whether the current user can delete this assessment
        /// Arabic: يمكن للمستخدم الحالي حذف هذا التقييم
        /// </summary>
        public bool CanDelete { get; set; }

        /// <summary>
        /// Whether the current user can approve this assessment
        /// Arabic: يمكن للمستخدم الحالي الموافقة على هذا التقييم
        /// </summary>
        public bool CanApprove { get; set; }

        /// <summary>
        /// Whether the current user can reject this assessment
        /// Arabic: يمكن للمستخدم الحالي رفض هذا التقييم
        /// </summary>
        public bool CanReject { get; set; }

        /// <summary>
        /// Whether the current user can distribute this assessment
        /// Arabic: يمكن للمستخدم الحالي توزيع هذا التقييم
        /// </summary>
        public bool CanDistribute { get; set; }

        /// <summary>
        /// Whether the current user can view results for this assessment
        /// Arabic: يمكن للمستخدم الحالي عرض نتائج هذا التقييم
        /// </summary>
        public bool CanViewResults { get; set; }

        /// <summary>
        /// Whether the current user can respond to this assessment
        /// Arabic: يمكن للمستخدم الحالي الرد على هذا التقييم
        /// </summary>
        public bool CanRespond { get; set; }



        /// <summary>
        /// Priority level for display (High, Medium, Low based on status)
        /// Arabic: مستوى الأولوية للعرض
        /// </summary>
        public string Priority { get; set; } = "Medium";
    }
}
