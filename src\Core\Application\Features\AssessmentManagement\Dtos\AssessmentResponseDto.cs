using Abstraction.Base.Dto;
using Application.Features.Resolutions.Dtos;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Entities.FundManagement;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Assessment Response
    /// Based on User Story 4: Respond to Assessment
    /// Contains board member response information
    /// </summary>
    public record AssessmentResponseDto : BaseDto
    {
        /// <summary>
        /// Assessment ID this response belongs to
        /// Arabic: معرف التقييم
        /// </summary>
        public int AssessmentId { get; set; }

        /// <summary>
        /// Assessment title for display
        /// Arabic: عنوان التقييم
        /// </summary>
        public string? AssessmentTitle { get; set; }

        /// <summary>
        /// User ID of the respondent
        /// Arabic: معرف المستخدم المجيب
        /// </summary>
        public int MemberId { get; set; }

        /// <summary>
        /// User name of the respondent
        /// Arabic: اسم المستخدم المجيب
        /// </summary>
        public string? MemberName { get; set; }
        public BoardMemberType MemberType { get; set; }
        public string DisplayedMemberType { get; set; }

        /// <summary>
        /// Response status
        /// Arabic: حالة الرد
        /// </summary>
        public ResponseStatus Status { get; set; }

        /// <summary>
        /// Response status display name
        /// Arabic: اسم حالة الرد للعرض
        /// </summary>
        public string? StatusDisplayName { get; set; }

        /// <summary>
        /// Individual answers to questions
        /// Arabic: الإجابات الفردية على الأسئلة
        /// </summary>
        public List<AssessmentAnswerDto>? Answers { get; set; }

        /// <summary>
        /// Response attachments (for attachment-type assessments)
        /// Arabic: مرفقات الرد
        /// </summary>
        public List<AssessmentResponseAttachmentDto>? Attachments { get; set; }

        /// <summary>
        /// General comments from the respondent
        /// Arabic: تعليقات عامة من المجيب
        /// </summary>
        public string? Comments { get; set; }

        /// <summary>
        /// Response submission date
        /// Arabic: تاريخ إرسال الرد
        /// </summary>
        public DateTime? SubmittedAt { get; set; }

        /// <summary>
        /// Response completion percentage
        /// Arabic: نسبة إكمال الرد
        /// </summary>
        public decimal CompletionPercentage { get; set; }

        /// <summary>
        /// Number of answered questions
        /// Arabic: عدد الأسئلة المجاب عليها
        /// </summary>
        public int AnsweredQuestions { get; set; }

        /// <summary>
        /// Total number of questions
        /// Arabic: العدد الإجمالي للأسئلة
        /// </summary>
        public int TotalQuestions { get; set; }

        /// <summary>
        /// Time spent on response in minutes
        /// Arabic: الوقت المستغرق في الرد بالدقائق
        /// </summary>
        public int? TimeSpentMinutes { get; set; }

        /// <summary>
        /// Whether the response can be edited
        /// Arabic: يمكن تعديل الرد
        /// </summary>
        public bool CanEdit { get; set; }

        /// <summary>
        /// Creation date
        /// Arabic: تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last updated date
        /// Arabic: تاريخ آخر تحديث
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Fund ID this assessment belongs to
        /// Arabic: معرف الصندوق
        /// </summary>
        public int FundId { get; set; }

        /// <summary>
        /// Fund name for display purposes
        /// Arabic: اسم الصندوق
        /// </summary>
        public string? FundName { get; set; }
    }



}
