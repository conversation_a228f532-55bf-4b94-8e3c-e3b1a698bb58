using Abstraction.Base.Dto;
using Application.Features.Resolutions.Dtos;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Assessment Results and Analytics
    /// Based on User Story 5: View Compiled Assessment Results
    /// Contains comprehensive assessment results, statistics, and analytics
    /// </summary>
    public record AssessmentResultsDto : AssessmentDto
    {
        /// <summary>
        /// Assessment questions (for Questionnaire type)
        /// Arabic: أسئلة التقييم
        /// </summary>
        public List<CreateAssessmentQuestionDto>? Questions { get; set; }

        /// <summary>
        /// Assessment attachments (for Attachment type or supporting documents)
        /// Arabic: مرفقات التقييم
        /// </summary>
        public AttachmentDto Attachment { get; set; }
        /// <summary>
        /// Assessment status history for audit trail
        /// Arabic: تاريخ حالة التقييم
        /// </summary>
        public List<AssessmentStatusHistoryDto>? StatusHistory { get; set; }

        /// <summary>
        /// Assessment responses (for management roles)
        /// Arabic: ردود التقييم
        /// </summary>
        public AssessmentResponseDto Response { get; set; }


        /// <summary>
        /// Whether can response  
        /// Arabic: يمكن الرد
        /// </summary>
        public bool CanResponse { get; set; }
    }





}
