using Abstraction.Base.Dto;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for Assessment Statistics
    /// Arabic: كائل نقل البيانات لإحصائيات التقييم
    /// </summary>
    public record AssessmentStatisticsDto
    {
        /// <summary>
        /// Total number of questions
        /// Arabic: العدد الإجمالي للأسئلة
        /// </summary>
        public int TotalQuestions { get; set; }

        /// <summary>
        /// Total expected responses
        /// Arabic: العدد المتوقع للردود
        /// </summary>
        public int TotalExpectedResponses { get; set; }

        /// <summary>
        /// Number of completed responses
        /// Arabic: عدد الردود المكتملة
        /// </summary>
        public int CompletedResponses { get; set; }

        /// <summary>
        /// Number of draft responses
        /// Arabic: عدد الردود المسودة
        /// </summary>
        public int DraftResponses { get; set; }

        /// <summary>
        /// Completion percentage
        /// Arabic: نسبة الإنجاز
        /// </summary>
        public decimal CompletionPercentage { get; set; }

        /// <summary>
        /// Average response time in minutes
        /// Arabic: متوسط وقت الرد بالدقائق
        /// </summary>
        public decimal? AverageResponseTime { get; set; }

    }
}
