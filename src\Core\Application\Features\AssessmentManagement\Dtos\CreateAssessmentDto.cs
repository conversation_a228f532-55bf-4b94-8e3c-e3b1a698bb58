using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for creating a new assessment
    /// Based on User Story 1: Create New Assessment
    /// Follows the same pattern as AddResolutionDto for consistency
    /// </summary>
    public record CreateAssessmentDto
    {
        /// <summary>
        /// Assessment title (required)
        /// Arabic: عنوان التقييم
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Assessment description (optional)
        /// Arabic: وصف التقييم
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Assessment type (Questionnaire or Attachment)
        /// Arabic: نوع التقييم
        /// </summary>
        public AssessmentType Type { get; set; }

        /// <summary>
        /// Fund ID this assessment belongs to
        /// Arabic: معرف الصندوق
        /// </summary>
        public int FundId { get; set; }



        /// <summary>
        /// Instructions for board members (optional)
        /// Arabic: تعليمات لأعضاء مجلس الإدارة
        /// </summary>
        public string? Instructions { get; set; }

        /// <summary>
        /// Whether the assessment allows anonymous responses
        /// Arabic: السماح بالردود المجهولة
        /// </summary>
        public bool AllowAnonymousResponses { get; set; } = false;

        /// <summary>
        /// Whether responses can be edited after submission
        /// Arabic: السماح بتعديل الردود
        /// </summary>
        public bool AllowResponseEditing { get; set; } = true;

        /// <summary>
        /// Assessment questions (for Questionnaire type)
        /// Arabic: أسئلة التقييم
        /// </summary>
        public List<CreateAssessmentQuestionDto>? Questions { get; set; }

        /// <summary>
        /// Assessment attachments (for Attachment type or supporting documents)
        /// Arabic: مرفقات التقييم
        /// </summary>
        public int? AttachmentId { get; set; }

        /// <summary>
        /// Whether to save as draft (true) or submit for approval (false)
        /// Arabic: حفظ كمسودة أم إرسال للموافقة
        /// </summary>
        public bool SaveAsDraft { get; set; } = true;
    }

}
