using Abstraction.Base.Dto;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for creating assessment question options
    /// Arabic: كائل نقل البيانات لإنشاء خيارات أسئلة التقييم
    /// </summary>
    public record CreateAssessmentOptionDto :BaseDto
    {
        /// <summary>
        /// Option text
        /// Arabic: نص الخيار
        /// </summary>
        public string OptionText { get; set; } = string.Empty;

        /// <summary>
        /// Option order/sequence
        /// Arabic: ترتيب الخيار
        /// </summary>
        public int Order { get; set; }
        /// <summary>
        /// Option order/sequence
        /// Arabic: ترتيب الخيار
        /// </summary>
        public bool? IsSelected { get; set; }

    }
}
