using Abstraction.Base.Dto;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for creating assessment questions
    /// Arabic: كائل نقل البيانات لإنشاء أسئلة التقييم
    /// </summary>
    public record CreateAssessmentQuestionDto :BaseDto
    {
        public int? AssessmentId { get; set; }

        /// <summary>
        /// Question text
        /// Arabic: نص السؤال
        /// </summary>
        public string QuestionText { get; set; } = string.Empty;

        /// <summary>
        /// Question type (SingleChoice, MultiChoice, Text)
        /// Arabic: نوع السؤال
        /// </summary>
        public QuestionType Type { get; set; }

        /// <summary>
        /// Question order/sequence
        /// Arabic: ترتيب السؤال
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// Whether the question is required
        /// Arabic: السؤال مطلوب
        /// </summary>
        public bool IsRequired { get; set; } = true;

        /// <summary>
        /// Question options (for SingleChoice and MultiChoice types)
        /// Arabic: خيارات السؤال
        /// </summary>
        public List<CreateAssessmentOptionDto>? Options { get; set; }
        public string QuestionAnswer { get; set; }
    }
}
