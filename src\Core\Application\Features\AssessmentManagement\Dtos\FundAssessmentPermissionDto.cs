namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for fund permission information for the current user
    /// Contains user's permissions and role information within a specific fund context
    /// Based on assessment management requirements and Clean Architecture principles
    /// Supports Arabic/English localization
    /// </summary>
    public record FundAssessmentPermissionDto
    {
        /// <summary>
        /// The name of the fund
        /// Arabic: اسم الصندوق
        /// </summary>
        public string FundName { get; set; } = string.Empty;

        /// <summary>
        /// Whether the current user is a board member of this fund
        /// Arabic: هل المستخدم الحالي عضو مجلس إدارة في هذا الصندوق
        /// </summary>
        public bool IsBoardMember { get; set; } = false;

        /// <summary>
        /// The board member ID if user is a board member, null otherwise
        /// Arabic: معرف عضو مجلس الإدارة إذا كان المستخدم عضو مجلس إدارة، فارغ خلاف ذلك
        /// </summary>
        public int? BoardMemberId { get; set; }

        /// <summary>
        /// Whether the user can create assessments (true if user is FundManager)
        /// Arabic: هل يمكن للمستخدم إنشاء تقييمات (صحيح إذا كان المستخدم مدير صندوق)
        /// </summary>
        public bool CanAdd { get; set; } = false;
    }
}
