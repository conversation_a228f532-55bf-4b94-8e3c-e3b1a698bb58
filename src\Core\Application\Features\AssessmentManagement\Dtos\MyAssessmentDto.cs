using Abstraction.Base.Dto;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for personal assessment view (User Story 6)
    /// Arabic: كائل نقل البيانات لعرض التقييم الشخصي
    /// </summary>
    public record MyAssessmentDto : BaseDto
    {
        /// <summary>
        /// Assessment title
        /// Arabic: عنوان التقييم
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Assessment description
        /// Arabic: وصف التقييم
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Assessment type
        /// Arabic: نوع التقييم
        /// </summary>
        public AssessmentType Type { get; set; }

        /// <summary>
        /// Assessment status
        /// Arabic: حالة التقييم
        /// </summary>
        public AssessmentStatus Status { get; set; }

        /// <summary>
        /// Fund name
        /// Arabic: اسم الصندوق
        /// </summary>
        public string FundName { get; set; } = string.Empty;



        /// <summary>
        /// Assignment date
        /// Arabic: تاريخ التكليف
        /// </summary>
        public DateTime AssignedAt { get; set; }

        /// <summary>
        /// My response status
        /// Arabic: حالة ردي
        /// </summary>
        public ResponseStatus? MyResponseStatus { get; set; }

        /// <summary>
        /// My response submission date
        /// Arabic: تاريخ إرسال ردي
        /// </summary>
        public DateTime? MyResponseSubmittedAt { get; set; }

        /// <summary>
        /// Whether I can respond to this assessment
        /// Arabic: يمكنني الرد على هذا التقييم
        /// </summary>
        public bool CanRespond { get; set; }

        /// <summary>
        /// Whether I can edit my response
        /// Arabic: يمكنني تعديل ردي
        /// </summary>
        public bool CanEditResponse { get; set; }



        /// <summary>
        /// Priority level
        /// Arabic: مستوى الأولوية
        /// </summary>
        public string Priority { get; set; } = "Medium";
    }
}
