using Abstraction.Base.Dto;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// Single assessment response DTO following Clean DTOs template patterns
    /// Used for single entity API responses with display properties
    /// Based on requirements for assessment list display
    /// Follows the same pattern as SingleResolutionResponse for consistency
    /// </summary>
    public record SingleAssessmentResponse : BaseDto
    {
        /// <summary>
        /// Creator user ID for permission checks
        /// </summary>
        public int CreatorID { get; set; }

        /// <summary>
        /// Fund name for display
        /// </summary>
        public string FundName { get; set; } = string.Empty;

        /// <summary>
        /// Assessment title
        /// Arabic: عنوان التقييم
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Assessment description (optional)
        /// Arabic: وصف التقييم
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Assessment type
        /// Arabic: نوع التقييم
        /// </summary>
        public AssessmentType Type { get; set; }

        /// <summary>
        /// Current status of the assessment
        /// </summary>
        public AssessmentStatus Status { get; set; }

        /// <summary>
        /// Assessment type information for display
        /// </summary>
        public AssessmentTypeDto? AssessmentType { get; set; }

        /// <summary>
        /// Last update date for sorting and display
        /// </summary>
        public DateTime? LastUpdated { get; set; }

        /// <summary>
        /// Assessment status identifier
        /// Numeric value of the assessment status enum
        /// </summary>
        public int StatusId { get; set; }

        /// <summary>
        /// Assessment status information with localization
        /// </summary>
        public AssessmentStatusDto? AssessmentStatus { get; set; }

        /// <summary>
        /// Assessment status name with localization
        /// </summary>
        public string? StatusName { get; set; }
        /// <summary>
        /// Due date for assessment responses
        /// Arabic: تاريخ الاستحقاق
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// Number of questions (for Questionnaire type)
        /// Arabic: عدد الأسئلة
        /// </summary>
        public int QuestionCount { get; set; }

        /// <summary>
        /// Number of attachments
        /// Arabic: عدد المرفقات
        /// </summary>
        public int AttachmentCount { get; set; }

        /// <summary>
        /// Total number of expected responses
        /// Arabic: العدد الإجمالي للردود المتوقعة
        /// </summary>
        public int TotalExpectedResponses { get; set; }

        /// <summary>
        /// Number of completed responses
        /// Arabic: عدد الردود المكتملة
        /// </summary>
        public int CompletedResponses { get; set; }

        /// <summary>
        /// Response completion percentage
        /// Arabic: نسبة إكمال الردود
        /// </summary>
        public decimal CompletionPercentage { get; set; }

        /// <summary>
        /// Created by user name
        /// Arabic: اسم المنشئ
        /// </summary>
        public string CreatedByName { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if current user can edit the assessment
        /// </summary>
        public bool CanEdit { get; set; }

        /// <summary>
        /// Indicates if current user can delete the assessment
        /// </summary>
        public bool CanDelete { get; set; }

        /// <summary>
        /// created date for assessment responses
        /// Arabic: تاريخ الانشاء
        /// </summary>
        public DateTime? CreatedAt { get; set; }
    }
}
