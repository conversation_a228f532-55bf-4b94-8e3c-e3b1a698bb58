using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Dtos
{
    /// <summary>
    /// DTO for submitting assessment response
    /// Based on User Story 4: Respond to Assessment
    /// </summary>
    public record SubmitAssessmentResponseDto
    {
        /// <summary>
        /// Assessment ID to respond to
        /// Arabic: معرف التقييم للرد عليه
        /// </summary>
        public int AssessmentId { get; set; }
        /// <summary>
        /// Assessment type to respond to
        /// Arabic: نوع التقييم للرد عليه
        /// </summary>
        public AssessmentType AssessmentType { get; set; }

        /// <summary>
        /// Individual answers to questions
        /// Arabic: الإجابات الفردية على الأسئلة
        /// </summary>
        public List<SubmitAssessmentAnswerDto>? Answers { get; set; }

        /// <summary>
        /// Whether to save as draft (true) or submit final response (false)
        /// Arabic: حفظ كمسودة أم إرسال الرد النهائي
        /// </summary>
        public bool SaveAsDraft { get; set; } = false;
    }
}
