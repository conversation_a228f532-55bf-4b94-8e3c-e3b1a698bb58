namespace Application.Features.AssessmentManagement.Helpers
{
    /// <summary>
    /// Helper class to hold assessment permission flags
    /// Used for calculating user permissions based on role and assessment status
    /// Arabic: فئة مساعدة لحفظ أذونات التقييم
    /// </summary>
    public class AssessmentPermissions
    {
        /// <summary>
        /// Indicates if current user can edit the assessment
        /// Arabic: يمكن للمستخدم الحالي تعديل التقييم
        /// </summary>
        public bool CanEdit { get; set; }

        /// <summary>
        /// Indicates if current user can delete the assessment
        /// Arabic: يمكن للمستخدم الحالي حذف التقييم
        /// </summary>
        public bool CanDelete { get; set; }

        /// <summary>
        /// Indicates if current user can approve the assessment
        /// Arabic: يمكن للمستخدم الحالي الموافقة على التقييم
        /// </summary>
        public bool CanApprove { get; set; }

        /// <summary>
        /// Indicates if current user can reject the assessment
        /// Arabic: يمكن للمستخدم الحالي رفض التقييم
        /// </summary>
        public bool CanReject { get; set; }

        /// <summary>
        /// Indicates if current user can distribute the assessment
        /// Arabic: يمكن للمستخدم الحالي توزيع التقييم
        /// </summary>
        public bool CanDistribute { get; set; }

        /// <summary>
        /// Indicates if current user can answer/respond to the assessment
        /// Arabic: يمكن للمستخدم الحالي الإجابة على التقييم
        /// </summary>
        public bool CanAnswer { get; set; }

        /// <summary>
        /// Default constructor with all permissions set to false
        /// </summary>
        public AssessmentPermissions()
        {
            CanEdit = false;
            CanDelete = false;
            CanApprove = false;
            CanReject = false;
            CanDistribute = false;
            CanAnswer = false;
        }
    }
}
