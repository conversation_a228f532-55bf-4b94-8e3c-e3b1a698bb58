using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Application.Features.AssessmentManagement.Dtos;

namespace Application.Features.AssessmentManagement.Queries.GetAssessmentDetails
{
    /// <summary>
    /// Query to get comprehensive assessment details with complete hierarchy
    /// Follows CQRS pattern for read operations
    /// Returns assessment with questions, options, responses, answers, and answer options
    /// Arabic: استعلام للحصول على تفاصيل التقييم الشاملة مع التسلسل الهرمي الكامل
    /// </summary>
    public record GetAssessmentDetailsQuery : IQuery<BaseResponse<AssessmentDetailsDto>>
    {
        /// <summary>
        /// Assessment ID to retrieve comprehensive details for
        /// Arabic: معرف التقييم لاسترجاع التفاصيل الشاملة له
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Constructor for GetAssessmentDetailsQuery
        /// </summary>
        /// <param name="id">Assessment ID</param>
        public GetAssessmentDetailsQuery(int id)
        {
            Id = id;
        }

        /// <summary>
        /// Parameterless constructor for model binding
        /// </summary>
        public GetAssessmentDetailsQuery()
        {
        }
    }
}
