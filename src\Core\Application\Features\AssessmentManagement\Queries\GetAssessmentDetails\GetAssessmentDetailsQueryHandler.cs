using Abstraction.Base.Response;
using Abstraction.Constants;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Application.Common.Helpers;
using Application.Features.AssessmentManagement.Dtos;
using AutoMapper;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Helpers;
using Domain.Services.Audit;
using MediatR;
using Microsoft.Extensions.Localization;
using Resources;

namespace Application.Features.AssessmentManagement.Queries.GetAssessmentDetails
{
    /// <summary>
    /// Handler for GetAssessmentDetailsQuery
    /// Retrieves comprehensive assessment details with complete hierarchy including responses and answers
    /// Follows CQRS pattern and existing architectural conventions
    /// Arabic: معالج استعلام الحصول على تفاصيل التقييم الشاملة
    /// </summary>
    public class GetAssessmentDetailsQueryHandler : BaseResponseHand<PERSON>, IQueryHandler<GetAssessmentDetailsQuery, BaseResponse<AssessmentDetailsDto>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        private readonly IAuditLocalizationService _localizationService;
        #endregion

        #region Constructor
        public GetAssessmentDetailsQueryHandler(
            ILoggerManager logger,
            IRepositoryManager repository,
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService,
            IAuditLocalizationService localizationService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
            _localizationService = localizationService ?? throw new ArgumentNullException(nameof(localizationService));
        }
        #endregion

        #region Handle Method
        public async Task<BaseResponse<AssessmentDetailsDto>> Handle(GetAssessmentDetailsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Getting comprehensive assessment details for ID: {request.Id}");

                // 1. Validate current user authorization
                var currentUserId = _currentUserService.UserId;

                if (!currentUserId.HasValue)
                {
                    _logger.LogWarn("Current user ID is null");
                    return Unauthorized<AssessmentDetailsDto>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 2. Validate current user exists in database
                var currentUser = await _repository.User.GetByIdAsync<Domain.Entities.Users.User>(currentUserId.Value, trackChanges: false);
                if (currentUser == null)
                {
                    _logger.LogWarn($"Current user not found in database with ID: {currentUserId.Value}");
                    return Unauthorized<AssessmentDetailsDto>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 3. Get assessment with comprehensive data including responses and answers
                var assessment = await _repository.Assessment.GetAssessmentWithDetailsAsync(request.Id, trackChanges: false);
                if (assessment == null)
                {
                    _logger.LogWarn($"Assessment not found with ID: {request.Id}");
                    return NotFound<AssessmentDetailsDto>(_localizer[SharedResourcesKey.AssessmentNotFound]);
                }
                var userRole = await GetUserFundRole(assessment.FundId, (int)currentUserId);

                // 4. Check user authorization to view comprehensive assessment details
                // TODO: Implement role-based authorization logic based on business requirements
                // For now, allowing all authenticated users to view comprehensive assessment details
                // This should be enhanced based on specific business rules

                // 5. Map to comprehensive DTO
                var assessmentDto = _mapper.Map<AssessmentDetailsDto>(assessment);

                // 6. Calculate and set statistics
                if (assessmentDto.Statistics == null)
                {
                    assessmentDto.Statistics = new AssessmentStatisticsDto();
                }
                // 7. Set action permissions for all assessments based on user role
                await SetActionPermissionsForAssessment(assessmentDto, currentUserId, userRole, assessment.FundId);
                
                CalculateStatistics(assessment, assessmentDto.Statistics);
                foreach (var item in assessmentDto.StatusHistory)
                {
                    item.DisplayedAction = _localizationService.GetLocalizedActionName(item.Action, null);
                    item.DisplayedStatus = _localizationService.GetLocalizedStatusName(item.Status, null);
                    item.DisplayedUserRole = _localizationService.GetLocalizedRole(item.UserRole, null);
                }
                assessmentDto.Responses.ForEach(r => r.DisplayedMemberType = LocalizationHelper.GetBoardMemberTypeDisplay(r.MemberType, _localizer));
                _logger.LogInfo($"Successfully retrieved comprehensive assessment details for ID: {request.Id}");
                return Success(assessmentDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving comprehensive assessment details for ID: {request.Id}");
                return ServerError<AssessmentDetailsDto>(_localizer[SharedResourcesKey.AnErrorIsOccurredWhileSavingData]);
            }
        }
        #endregion

        #region Helper Methods
        /// <summary>
        /// Determines the current user's role within a specific fund context
        /// Checks FundBoardSecretary table, Fund.LegalCouncilId field, and FundManager table
        /// Follows Clean Architecture and CQRS patterns with proper repository usage
        /// Same implementation as Resolution ListQueryHandler
        /// </summary>
        /// <param name="fundId">The fund ID to check roles for</param>
        /// <param name="currentUserId">The current user ID to check roles for</param>
        /// <returns>User role in the fund, or None if no roles</returns>
        private async Task<Roles> GetUserFundRole(int fundId, int currentUserId)
        {
            try
            {
                _logger.LogInfo($"Checking fund roles for User ID: {currentUserId} in Fund ID: {fundId}");

                var userRole = Roles.None;
                // Get fund details with all related entities
                var fundDetails = await _repository.Funds.ViewFundUsers(fundId, trackChanges: false);
                if (fundDetails == null)
                {
                    _logger.LogWarn($"Fund not found with ID: {fundId}");
                    return Roles.None;
                }

                // 1. Check if user is Legal Council for the fund
                if (fundDetails.LegalCouncilId == currentUserId)
                {
                    userRole = Roles.LegalCouncil;
                    _logger.LogInfo($"User ID: {currentUserId} is Legal Council for Fund ID: {fundId}");
                }

                // 2. Check if user is a Fund Manager for the fund
                if (fundDetails.FundManagers != null && fundDetails.FundManagers.Count > 0)
                {
                    var isFundManager = fundDetails.FundManagers.Any(fm => fm.UserId == currentUserId);
                    if (isFundManager)
                    {
                        userRole = Roles.FundManager;
                        _logger.LogInfo($"User ID: {currentUserId} is Fund Manager for Fund ID: {fundId}");
                    }
                }

                // 3. Check if user is a Board Secretary for the fund
                if (fundDetails.FundBoardSecretaries != null && fundDetails.FundBoardSecretaries.Count > 0)
                {
                    var isBoardSecretary = fundDetails.FundBoardSecretaries.Any(bs => bs.UserId == currentUserId);
                    if (isBoardSecretary)
                    {
                        userRole = Roles.BoardSecretary;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Secretary for Fund ID: {fundId}");
                    }
                }
                // 3. Check if user is a Board Secretary for the fund
                if (fundDetails.BoardMembers != null && fundDetails.BoardMembers.Count > 0)
                {
                    var isMember = fundDetails.BoardMembers.Any(bs => bs.UserId == currentUserId);
                    if (isMember)
                    {
                        userRole = Roles.BoardMember;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Member for Fund ID: {fundId}");
                    }
                }
                _logger.LogInfo($"User ID: {currentUserId} has roles in Fund ID: {fundId}: '{userRole}'");

                return userRole;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking fund roles for User ID: {currentUserId} in Fund ID: {fundId}");
                return Roles.None;
            }
        }


        /// <summary>
        /// Calculates comprehensive statistics for the assessment
        /// </summary>
        private void CalculateStatistics(Assessment assessment, AssessmentStatisticsDto statistics)
        {
            // Calculate question statistics
            statistics.TotalQuestions = assessment.Questions?.Count ?? 0;

            // Calculate response statistics
            var responses = assessment.Responses?.ToList() ?? new List<AssessmentResponse>();
            statistics.TotalExpectedResponses = responses.Count;
            statistics.CompletedResponses = responses.Count(r => r.Status == Domain.Entities.AssessmentManagement.Enums.ResponseStatus.Completed);
            statistics.DraftResponses = responses.Count(r => r.Status == Domain.Entities.AssessmentManagement.Enums.ResponseStatus.Pending);

            // Calculate completion percentage
            if (statistics.TotalExpectedResponses > 0)
            {
                statistics.CompletionPercentage = Math.Round(
                    (decimal)statistics.CompletedResponses / statistics.TotalExpectedResponses * 100, 2);
            }

            // Calculate average response time (in minutes)
            var completedResponses = responses.Where(r => r.Status == Domain.Entities.AssessmentManagement.Enums.ResponseStatus.Completed && r.SubmissionDate.HasValue).ToList();
            if (completedResponses.Any())
            {
                var totalMinutes = completedResponses.Sum(r =>
                {
                    var startTime = r.CreatedAt;
                    var endTime = r.SubmissionDate!.Value;
                    return (decimal)(endTime - startTime).TotalMinutes;
                });

                statistics.AverageResponseTime = Math.Round(totalMinutes / completedResponses.Count, 2);
            }

        }

        /// <summary>
        /// Sets action permissions for assessment based on user role and assessment status
        /// Optimized to minimize database calls by grouping assessments by fund
        /// </summary>
        /// <param name="assessment">assessment DTO to set permissions for</param>
        /// <param name="userRole">Current user's role</param>
        /// <param name="currentUserId">Current user's ID</param>
        /// <param name="fundId">Fund ID</param>
        private async Task SetActionPermissionsForAssessment(AssessmentDetailsDto assessment, int? currentUserId, Roles userFundRole, int fundId)
        {
            if (!currentUserId.HasValue) return;

            var isFundManager = userFundRole == Roles.FundManager;
            var isLegalCouncilOrBoardSecretary = userFundRole == Roles.LegalCouncil || userFundRole == Roles.BoardSecretary;

            // Default all actions to false

            assessment.CanEdit = false;
            assessment.CanDelete = false;
            assessment.CanReject = false;
            assessment.CanApprove = false;
            assessment.CanDistribute = false;


            // Set permissions based on status and role
            switch (assessment.Status)
            {
                case AssessmentStatus.Draft:
                    assessment.CanEdit = isFundManager && _currentUserService.UserId == currentUserId;
                    assessment.CanDelete = _currentUserService.UserId == currentUserId && isFundManager;
                    break;

                case AssessmentStatus.WaitingForApproval:
                    assessment.CanEdit = isFundManager;
                    assessment.CanApprove = isLegalCouncilOrBoardSecretary;
                    assessment.CanReject = isLegalCouncilOrBoardSecretary;
                    break;

                case AssessmentStatus.Rejected:
                    assessment.CanEdit = isFundManager;
                    break;

                case AssessmentStatus.Approved:
                    assessment.CanDistribute = isFundManager;
                    break;
            }

        }

        
        #endregion
    }
}
