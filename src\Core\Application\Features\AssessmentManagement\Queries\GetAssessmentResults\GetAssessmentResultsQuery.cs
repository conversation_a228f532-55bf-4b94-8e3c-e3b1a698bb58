using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Application.Features.AssessmentManagement.Dtos;
using Abstraction.Base.Dto;

namespace Application.Features.AssessmentManagement.Queries.GetAssessmentResults
{
    /// <summary>
    /// Query for getting compiled assessment results and analytics
    /// Based on User Story 5: View Compiled Assessment Results
    /// Implements IQuery interface for CQRS pattern integration with MediatR
    /// Follows the same pattern as other analytics queries for consistency
    /// </summary>
    public record GetAssessmentResultsQuery : IQuery<BaseResponse<AssessmentResultsDto>>
    {
        public int AssessmentId { get; set; }
        public int BoardMemberId { get; set; }
    }
}
