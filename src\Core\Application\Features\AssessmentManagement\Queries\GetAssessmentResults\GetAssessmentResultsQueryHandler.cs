using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using AutoMapper;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contract.Service;
using Application.Features.AssessmentManagement.Dtos;
using Domain.Entities.AssessmentManagement.Enums;
using Abstraction.Constants;

namespace Application.Features.AssessmentManagement.Queries.GetAssessmentResults
{
    /// <summary>
    /// Handler for GetAssessmentResultsQuery
    /// Implements business logic for retrieving compiled assessment results and analytics
    /// Based on User Story 5: View Compiled Assessment Results and Clean Architecture principles
    /// Follows the same pattern as other analytics query handlers for consistency
    /// </summary>
    public class GetAssessmentResultsQueryHandler : BaseResponseHandler, IQueryHandler<GetAssessmentResultsQuery, BaseResponse<AssessmentResultsDto>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructor
        public GetAssessmentResultsQueryHandler(
            ILoggerManager logger,
            IRepositoryManager repository,
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _logger = logger;
            _repository = repository;
            _mapper = mapper;
            _localizer = localizer;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<AssessmentResultsDto>> Handle(GetAssessmentResultsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Getting assessment results for Assessment ID: {request.AssessmentId}, Board Member ID: {request.BoardMemberId}");

                // Get assessment with comprehensive data including responses and answers
                var assessment = await _repository.Assessment.GetAssessmentWithComprehensiveDetailsAsync(request.AssessmentId, trackChanges: false);
                if (assessment == null)
                {
                    return NotFound<AssessmentResultsDto>(_localizer[SharedResourcesKey.AssessmentNotFound]);
                }

                // Check user permissions
                if (!CanUserViewResults(assessment))
                {
                    return Unauthorized<AssessmentResultsDto>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // Get the specific board member's response
                var boardMemberResponse = assessment.Responses?
                    .FirstOrDefault(r => r.BoardMemberId == request.BoardMemberId);

                if (boardMemberResponse == null)
                {
                    return NotFound<AssessmentResultsDto>(_localizer[SharedResourcesKey.AssessmentResponseNotFound]);
                }

                // Map to results DTO
                var resultsDto = _mapper.Map<AssessmentResultsDto>(assessment);

                // Set the specific response data for this board member
                resultsDto.Response = MapBoardMemberResponse(boardMemberResponse);
                var userRole = await GetUserFundRole(assessment.FundId, _currentUserService.UserId.GetValueOrDefault());
                var isBoardMember = userRole == Roles.BoardMember;

                resultsDto.CanResponse = isBoardMember && assessment.Status == AssessmentStatus.Active && resultsDto.Response.Status == ResponseStatus.Pending;
                _logger.LogInfo($"Assessment results generated successfully for Assessment ID: {request.AssessmentId}, Board Member ID: {request.BoardMemberId}");

                return Success(resultsDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting assessment results: {ex.Message}");
                return ServerError<AssessmentResultsDto>(_localizer[SharedResourcesKey.SystemErrorRetrievingData]);
            }
        }
        #endregion

        #region Private Methods
        private bool CanUserViewResults(Domain.Entities.AssessmentManagement.Assessment assessment)
        {
            // For now, allow all authenticated users to view results
            // This should be enhanced based on specific business rules
            return true;
        }

        private AssessmentResponseDto MapBoardMemberResponse(Domain.Entities.AssessmentManagement.AssessmentResponse response)
        {
            var responseDto = _mapper.Map<AssessmentResponseDto>(response);

            // Map answers with their selected options and text answers
            if (response.Answers?.Any() == true)
            {
                responseDto.Answers = response.Answers.Select(answer => new AssessmentAnswerDto
                {
                    Id = answer.Id,
                    QuestionId = answer.QuestionId,
                    TextAnswer = answer.AnswerValue,
                    SelectedOptions = answer.SelectedOptions?.Select(ao => new AssessmentAnswerOptionDto
                    {
                        Id = ao.Id,
                        OptionId = ao.OptionId,
                        OptionText = ao.Option?.Value
                    }).ToList() ?? new List<AssessmentAnswerOptionDto>()
                }).ToList();
            }

            return responseDto;
        }
        /// <summary>
        /// Determines the current user's role within a specific fund context
        /// Checks FundBoardSecretary table, Fund.LegalCouncilId field, and FundManager table
        /// Follows Clean Architecture and CQRS patterns with proper repository usage
        /// Same implementation as Resolution ListQueryHandler
        /// </summary>
        /// <param name="fundId">The fund ID to check roles for</param>
        /// <param name="currentUserId">The current user ID to check roles for</param>
        /// <returns>User role in the fund, or None if no roles</returns>
        private async Task<Roles> GetUserFundRole(int fundId, int currentUserId)
        {
            try
            {
                _logger.LogInfo($"Checking fund roles for User ID: {currentUserId} in Fund ID: {fundId}");

                var userRole = Roles.None;
                // Get fund details with all related entities
                var fundDetails = await _repository.Funds.ViewFundUsers(fundId, trackChanges: false);
                if (fundDetails == null)
                {
                    _logger.LogWarn($"Fund not found with ID: {fundId}");
                    return Roles.None;
                }

                // 1. Check if user is Legal Council for the fund
                if (fundDetails.LegalCouncilId == currentUserId)
                {
                    userRole = Roles.LegalCouncil;
                    _logger.LogInfo($"User ID: {currentUserId} is Legal Council for Fund ID: {fundId}");
                }

                // 2. Check if user is a Fund Manager for the fund
                if (fundDetails.FundManagers != null && fundDetails.FundManagers.Count > 0)
                {
                    var isFundManager = fundDetails.FundManagers.Any(fm => fm.UserId == currentUserId);
                    if (isFundManager)
                    {
                        userRole = Roles.FundManager;
                        _logger.LogInfo($"User ID: {currentUserId} is Fund Manager for Fund ID: {fundId}");
                    }
                }

                // 3. Check if user is a Board Secretary for the fund
                if (fundDetails.FundBoardSecretaries != null && fundDetails.FundBoardSecretaries.Count > 0)
                {
                    var isBoardSecretary = fundDetails.FundBoardSecretaries.Any(bs => bs.UserId == currentUserId);
                    if (isBoardSecretary)
                    {
                        userRole = Roles.BoardSecretary;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Secretary for Fund ID: {fundId}");
                    }
                }
                // 3. Check if user is a Board Secretary for the fund
                if (fundDetails.BoardMembers != null && fundDetails.BoardMembers.Count > 0)
                {
                    var isMember = fundDetails.BoardMembers.Any(bs => bs.UserId == currentUserId);
                    if (isMember)
                    {
                        userRole = Roles.BoardMember;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Member for Fund ID: {fundId}");
                    }
                }
                _logger.LogInfo($"User ID: {currentUserId} has roles in Fund ID: {fundId}: '{userRole}'");

                return userRole;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking fund roles for User ID: {currentUserId} in Fund ID: {fundId}");
                return Roles.None;
            }
        }
        private void CalculateStatistics(Domain.Entities.AssessmentManagement.Assessment assessment, AssessmentStatisticsDto statistics)
        {
            // Calculate question statistics
            statistics.TotalQuestions = assessment.Questions?.Count ?? 0;

            // Calculate response statistics
            var responses = assessment.Responses?.ToList() ?? new List<Domain.Entities.AssessmentManagement.AssessmentResponse>();
            statistics.TotalExpectedResponses = responses.Count;
            statistics.CompletedResponses = responses.Count(r => r.Status == ResponseStatus.Completed);
            statistics.DraftResponses = responses.Count(r => r.Status == ResponseStatus.Pending);

            // Calculate completion percentage
            statistics.CompletionPercentage = statistics.TotalExpectedResponses > 0
                ? (decimal)statistics.CompletedResponses / statistics.TotalExpectedResponses * 100
                : 0;

        }
        #endregion
    }
}
