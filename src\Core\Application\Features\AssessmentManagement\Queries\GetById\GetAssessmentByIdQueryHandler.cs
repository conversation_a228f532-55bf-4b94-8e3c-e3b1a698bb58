using Abstraction.Base.Response;
using Abstraction.Constants;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Application.Features.AssessmentManagement.Dtos;
using Application.Features.AssessmentManagement.Helpers;
using Application.Base.Abstracts;
using AutoMapper;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Entities.FundManagement;
using Microsoft.Extensions.Localization;
using Resources;
using MediatR;

namespace Application.Features.AssessmentManagement.Queries.GetById
{
    /// <summary>
    /// Handler for GetAssessmentByIdQuery
    /// Retrieves assessment details by ID with proper authorization and error handling
    /// Follows CQRS pattern and existing architectural conventions
    /// Arabic: معالج استعلام الحصول على تفاصيل التقييم حسب المعرف
    /// </summary>
    public class GetAssessmentByIdQueryHandler : BaseResponse<PERSON><PERSON><PERSON>, IQueryHandler<GetAssessmentByIdQuery, BaseResponse<AssessmentByIdDto>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructor
        public GetAssessmentByIdQueryHandler(
            ILoggerManager logger,
            IRepositoryManager repository,
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _logger = logger;
            _repository = repository;
            _mapper = mapper;
            _localizer = localizer;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Handle Method
        public async Task<BaseResponse<AssessmentByIdDto>> Handle(GetAssessmentByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Getting assessment details for ID: {request.Id}");

                // 1. Validate current user authorization
                var currentUserId = _currentUserService.UserId;
                if (!currentUserId.HasValue)
                {
                    _logger.LogWarn("Current user ID is null");
                    return Unauthorized<AssessmentByIdDto>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 2. Validate current user exists in database
                var currentUser = await _repository.User.GetByIdAsync<Domain.Entities.Users.User>(currentUserId.Value, trackChanges: false);
                if (currentUser == null)
                {
                    _logger.LogWarn($"Current user not found in database with ID: {currentUserId.Value}");
                    return Unauthorized<AssessmentByIdDto>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                }

                // 3. Get assessment with all related data
                var assessment = await _repository.Assessment.GetAssessmentWithDetailsAsync(request.Id, trackChanges: false);
                if (assessment == null)
                {
                    _logger.LogWarn($"Assessment not found with ID: {request.Id}");
                    return NotFound<AssessmentByIdDto>(_localizer[SharedResourcesKey.AssessmentNotFound]);
                }

                // 4. Get fund details for role-based authorization
                var fundDetails = await _repository.Funds.ViewFundUsers(assessment.FundId, trackChanges: false);
                if (fundDetails == null)
                {
                    _logger.LogWarn($"Fund not found for assessment ID: {request.Id}");
                    return NotFound<AssessmentByIdDto>(_localizer[SharedResourcesKey.FundNotFound]);
                }

                // 5. Determine user's role in the fund context
                var userRole = await GetUserFundRole(fundDetails, currentUserId.Value);

                // 6. Check if user has existing response (for canAnswer permission)
                var userResponse = assessment.Responses?.FirstOrDefault(r => r.BoardMemberId == currentUserId.Value);

                // 7. Calculate user permissions based on role and assessment status
                var permissions = await CalculateAssessmentPermissions(assessment, currentUserId.Value, userRole, userResponse);

                // 8. Map to DTO
                var assessmentDto = _mapper.Map<AssessmentByIdDto>(assessment);

                // 9. Set permission properties
                assessmentDto.CanEdit = permissions.CanEdit;
                assessmentDto.CanDelete = permissions.CanDelete;
                assessmentDto.CanApprove = permissions.CanApprove;
                assessmentDto.CanReject = permissions.CanReject;
                assessmentDto.CanDistribute = permissions.CanDistribute;
                assessmentDto.CanAnswer = permissions.CanAnswer;

                _logger.LogInfo($"Successfully retrieved assessment details for ID: {request.Id}");
                return Success(assessmentDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving assessment details for ID: {request.Id}");
                return ServerError<AssessmentByIdDto>(_localizer[SharedResourcesKey.AnErrorIsOccurredWhileSavingData]);
            }
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Determines the current user's role within a specific fund context
        /// Checks FundBoardSecretary table, Fund.LegalCouncilId field, and FundManager table
        /// Follows Clean Architecture and CQRS patterns with proper repository usage
        /// </summary>
        /// <param name="fundDetails">The fund details to check roles for</param>
        /// <param name="currentUserId">The current user ID to check roles for</param>
        /// <returns>User's role in the fund context</returns>
        private async Task<Roles> GetUserFundRole(Fund fundDetails, int currentUserId)
        {
            try
            {
                _logger.LogInfo($"Checking fund roles for User ID: {currentUserId} in Fund ID: {fundDetails.Id}");

                var userRole = Roles.None;

                // 1. Check if user is Legal Council for the fund
                if (fundDetails.LegalCouncilId == currentUserId)
                {
                    userRole = Roles.LegalCouncil;
                    _logger.LogInfo($"User ID: {currentUserId} is Legal Council for Fund ID: {fundDetails.Id}");
                }

                // 2. Check if user is Board Secretary for the fund
                if (fundDetails.FundBoardSecretaries != null && fundDetails.FundBoardSecretaries.Count > 0)
                {
                    var isBoardSecretary = fundDetails.FundBoardSecretaries.Any(bs => bs.UserId == currentUserId);
                    if (isBoardSecretary)
                    {
                        userRole = Roles.BoardSecretary;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Secretary for Fund ID: {fundDetails.Id}");
                    }
                }

                // 3. Check if user is Fund Manager for the fund
                if (fundDetails.FundManagers != null && fundDetails.FundManagers.Count > 0)
                {
                    var isFundManager = fundDetails.FundManagers.Any(fm => fm.UserId == currentUserId);
                    if (isFundManager)
                    {
                        userRole = Roles.FundManager;
                        _logger.LogInfo($"User ID: {currentUserId} is Fund Manager for Fund ID: {fundDetails.Id}");
                    }
                }

                // 4. Check if user is Board Member for the fund
                if (fundDetails.BoardMembers != null && fundDetails.BoardMembers.Count > 0)
                {
                    var isBoardMember = fundDetails.BoardMembers.Any(bm => bm.UserId == currentUserId);
                    if (isBoardMember)
                    {
                        userRole = Roles.BoardMember;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Member for Fund ID: {fundDetails.Id}");
                    }
                }

                _logger.LogInfo($"User ID: {currentUserId} has role in Fund ID: {fundDetails.Id}: '{userRole}'");
                return userRole;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking fund roles for User ID: {currentUserId} in Fund ID: {fundDetails.Id}");
                return Roles.None;
            }
        }

        /// <summary>
        /// Calculates comprehensive assessment permissions based on user role and assessment status
        /// Implements business rules from user stories for all permission types
        /// </summary>
        /// <param name="assessment">Assessment entity with status and creator information</param>
        /// <param name="currentUserId">Current user ID</param>
        /// <param name="userRole">User's role in the fund context</param>
        /// <param name="userResponse">User's existing response (if any)</param>
        /// <returns>AssessmentPermissions object with all permission flags</returns>
        private async Task<AssessmentPermissions> CalculateAssessmentPermissions(
            Assessment assessment,
            int currentUserId,
            Roles userRole,
            AssessmentResponse? userResponse = null)
        {
            var permissions = new AssessmentPermissions();

            try
            {
                _logger.LogInfo($"Calculating permissions for User ID: {currentUserId}, Role: {userRole}, Assessment Status: {assessment.Status}");

                // Role flags for cleaner logic
                var isFundManager = userRole == Roles.FundManager;
                var isLegalCouncilOrBoardSecretary = userRole == Roles.LegalCouncil || userRole == Roles.BoardSecretary;
                var isBoardMember = userRole == Roles.BoardMember;
                var isCreator = assessment.CreatedBy == currentUserId;

                // Calculate permissions based on assessment status
                switch (assessment.Status)
                {
                    case AssessmentStatus.Draft:
                        // Only Fund Manager who created the assessment can edit/delete
                        permissions.CanEdit = isFundManager && isCreator;
                        permissions.CanDelete = isFundManager && isCreator;
                        // No other actions allowed for Draft status
                        break;

                    case AssessmentStatus.WaitingForApproval:
                        // Legal Council and Board Secretary can edit, delete, approve, reject
                        permissions.CanEdit = isLegalCouncilOrBoardSecretary;
                        permissions.CanDelete = isLegalCouncilOrBoardSecretary;
                        permissions.CanApprove = isLegalCouncilOrBoardSecretary;
                        permissions.CanReject = isLegalCouncilOrBoardSecretary;
                        // No distribute or answer actions for this status
                        break;

                    case AssessmentStatus.Rejected:
                        // Legal Council and Board Secretary can edit and delete rejected assessments
                        permissions.CanEdit = isLegalCouncilOrBoardSecretary;
                        permissions.CanDelete = isLegalCouncilOrBoardSecretary;
                        // No other actions allowed for Rejected status
                        break;

                    case AssessmentStatus.Approved:
                        // Only Fund Manager can distribute approved assessments
                        permissions.CanDistribute = isFundManager;
                        // No other actions allowed for Approved status
                        break;

                    case AssessmentStatus.Active:
                        // Board Members can answer if they haven't already responded
                        permissions.CanAnswer = isBoardMember && userResponse == null;
                        // No other actions allowed for Active status
                        break;

                    case AssessmentStatus.Completed:
                        // All assessments are read-only when completed
                        // No actions allowed
                        break;

                    default:
                        // Unknown status, no actions allowed
                        _logger.LogWarn($"Unknown assessment status: {assessment.Status}");
                        break;
                }

                _logger.LogInfo($"Calculated permissions - CanEdit: {permissions.CanEdit}, CanDelete: {permissions.CanDelete}, " +
                              $"CanApprove: {permissions.CanApprove}, CanReject: {permissions.CanReject}, " +
                              $"CanDistribute: {permissions.CanDistribute}, CanAnswer: {permissions.CanAnswer}");

                return permissions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error calculating assessment permissions for User ID: {currentUserId}");
                return new AssessmentPermissions(); // Return all false permissions on error
            }
        }
        #endregion
    }
}
