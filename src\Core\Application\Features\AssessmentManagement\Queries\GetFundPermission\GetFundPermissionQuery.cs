using Abstraction.Base.Response;
using Application.Base.Abstracts;
using Application.Features.AssessmentManagement.Dtos;

namespace Application.Features.AssessmentManagement.Queries.GetFundPermission
{
    /// <summary>
    /// Query to get fund permission information for the current user
    /// Returns user's permissions and role information within a specific fund context
    /// Based on assessment management requirements and CQRS pattern
    /// Supports Arabic/English localization
    /// </summary>
    public record GetFundPermissionQuery : IQuery<BaseResponse<FundAssessmentPermissionDto>>
    {
        /// <summary>
        /// Fund identifier to check permissions for
        /// Arabic: معرف الصندوق للتحقق من الصلاحيات له
        /// </summary>
        public int FundId { get; set; }
    }
}
