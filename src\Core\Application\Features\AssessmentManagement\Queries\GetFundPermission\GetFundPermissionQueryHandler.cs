using Abstraction.Base.Response;
using Application.Base.Abstracts;
using Application.Features.AssessmentManagement.Dtos;
using Abstraction.Contract.Service;
using Abstraction.Constants;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;

namespace Application.Features.AssessmentManagement.Queries.GetFundPermission
{
    /// <summary>
    /// Handler for getting fund permission information for the current user
    /// Returns user's permissions and role information within a specific fund context
    /// Based on assessment management requirements and CQRS pattern
    /// Supports Arabic/English localization and role-based authorization
    /// </summary>
    public class GetFundPermissionQueryHandler : BaseResponseHandler, IQueryHandler<GetFundPermissionQuery, BaseResponse<FundAssessmentPermissionDto>>
    {
        #region Fields
        private readonly ICurrentUserService _currentUserService;
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        #endregion

        #region Constructor
        public GetFundPermissionQueryHandler(
            ICurrentUserService currentUserService,
            ILoggerManager logger,
            IRepositoryManager repository)
        {
            _currentUserService = currentUserService;
            _logger = logger;
            _repository = repository;
        }
        #endregion

        #region Handle Method
        public async Task<BaseResponse<FundAssessmentPermissionDto>> Handle(GetFundPermissionQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Getting fund permission information for Fund ID: {request.FundId}");

                // 1. Validate input
                if (request.FundId <= 0)
                {
                    _logger.LogWarn($"Invalid fund ID: {request.FundId}");
                    return BadRequest<FundAssessmentPermissionDto>("Invalid fund ID format");
                }

                // 2. Get current user ID
                var currentUserId = _currentUserService.UserId;
                if (!currentUserId.HasValue)
                {
                    _logger.LogWarn("Current user ID is null");
                    return BadRequest<FundAssessmentPermissionDto>("User not authenticated");
                }

                // 3. Get fund details
                var fundDetails = await _repository.Funds.ViewFundUsers(request.FundId, trackChanges: false);
                if (fundDetails == null)
                {
                    _logger.LogWarn($"Fund not found with ID: {request.FundId}");
                    return NotFound<FundAssessmentPermissionDto>("Fund not found");
                }

                // 4. Determine user's role in the fund context
                var userRole = await GetUserFundRole(request.FundId, currentUserId.Value);

                // 5. Initialize response DTO
                var response = new FundAssessmentPermissionDto
                {
                    FundName = fundDetails.Name,
                    CanAdd = userRole == Roles.FundManager,
                    IsBoardMember = userRole == Roles.BoardMember,
                    BoardMemberId = null
                };

                // 6. If user is a board member, get board member details
                if (userRole == Roles.BoardMember)
                {
                    var boardMember = await _repository.BoardMembers.GetBoardMemberByUserId(currentUserId.Value, request.FundId);
                    if (boardMember != null)
                    {
                        response.BoardMemberId = boardMember.Id;
                        _logger.LogInfo($"User ID: {currentUserId.Value} is Board Member with ID: {boardMember.Id} for Fund ID: {request.FundId}");
                    }
                    else
                    {
                        _logger.LogWarn($"Board member record not found for User ID: {currentUserId.Value} in Fund ID: {request.FundId}");
                    }
                }

                _logger.LogInfo($"Fund permission retrieved successfully for User ID: {currentUserId.Value} in Fund ID: {request.FundId}");
                return Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting fund permission for Fund ID: {request.FundId}");
                return ServerError<FundAssessmentPermissionDto>("An error occurred while retrieving fund permission information");
            }
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Determines the current user's role within a specific fund context
        /// Checks FundBoardSecretary table, Fund.LegalCouncilId field, and FundManager table
        /// Follows the same pattern as used in resolution handlers
        /// </summary>
        /// <param name="fundId">The fund ID to check roles for</param>
        /// <param name="currentUserId">The current user ID to check roles for</param>
        /// <returns>User's role within the fund or Roles.None if no access</returns>
        private async Task<Roles> GetUserFundRole(int fundId, int currentUserId)
        {
            try
            {
                _logger.LogInfo($"Checking fund roles for User ID: {currentUserId} in Fund ID: {fundId}");

                var userRole = Roles.None;
                // Get fund details with all related entities
                var fundDetails = await _repository.Funds.ViewFundUsers(fundId, trackChanges: false);
                if (fundDetails == null)
                {
                    _logger.LogWarn($"Fund not found with ID: {fundId}");
                    return Roles.None;
                }

                // 1. Check if user is Legal Council for the fund
                if (fundDetails.LegalCouncilId == currentUserId)
                {
                    userRole = Roles.LegalCouncil;
                    _logger.LogInfo($"User ID: {currentUserId} is Legal Council for Fund ID: {fundId}");
                }

                // 2. Check if user is a Fund Manager for the fund
                if (fundDetails.FundManagers != null && fundDetails.FundManagers.Count > 0)
                {
                    var isFundManager = fundDetails.FundManagers.Any(fm => fm.UserId == currentUserId);
                    if (isFundManager)
                    {
                        userRole = Roles.FundManager;
                        _logger.LogInfo($"User ID: {currentUserId} is Fund Manager for Fund ID: {fundId}");
                    }
                }

                // 3. Check if user is a Board Secretary for the fund
                if (fundDetails.FundBoardSecretaries != null && fundDetails.FundBoardSecretaries.Count > 0)
                {
                    var isBoardSecretary = fundDetails.FundBoardSecretaries.Any(bs => bs.UserId == currentUserId);
                    if (isBoardSecretary)
                    {
                        userRole = Roles.BoardSecretary;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Secretary for Fund ID: {fundId}");
                    }
                }

                // 4. Check if user is a Board Member for the fund
                if (fundDetails.BoardMembers != null && fundDetails.BoardMembers.Count > 0)
                {
                    var isBoardMember = fundDetails.BoardMembers.Any(bs => bs.UserId == currentUserId);
                    if (isBoardMember)
                    {
                        userRole = Roles.BoardMember;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Member for Fund ID: {fundId}");
                    }
                }

                _logger.LogInfo($"User ID: {currentUserId} has role in Fund ID: {fundId}: '{userRole}'");
                return userRole;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking fund roles for User ID: {currentUserId} in Fund ID: {fundId}");
                return Roles.None;
            }
        }
        #endregion
    }
}
