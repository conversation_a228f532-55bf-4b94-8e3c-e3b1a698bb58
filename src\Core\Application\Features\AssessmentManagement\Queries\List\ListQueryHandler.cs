using Abstraction.Base.Response;
using Abstraction.Common.Wappers;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Application.Features.AssessmentManagement.Dtos;
using AutoMapper;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Services;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Constants;

namespace Application.Features.AssessmentManagement.Queries.List
{
    /// <summary>
    /// Handler for ListQuery to retrieve paginated assessment list
    /// Implements role-based access control and comprehensive filtering
    /// Based on Assessment requirements and user stories
    /// Follows the same pattern as Resolution ListQueryHandler for consistency
    /// </summary>
    public class ListQueryHandler : BaseResponseHand<PERSON>, IQueryHandler<ListQuery, PaginatedResult<SingleAssessmentResponse>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructor(s)
        public ListQueryHandler(
            IRepositoryManager repository,
            IMapper mapper,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _logger = logger;
            _repository = repository;
            _mapper = mapper;
            _localizer = localizer;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Functions
        public async Task<PaginatedResult<SingleAssessmentResponse>> Handle(ListQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo("Starting GetAssessmentsList operation");

                // 1. Get current user information for role-based filtering
                var currentUserId = _currentUserService.UserId;
                var userRole = await GetUserFundRole(request.FundId.Value, _currentUserService.UserId.Value);

                _logger.LogInfo($"Current User - ID: {currentUserId}, Roles: {string.Join(", ", userRole)}");

                // 2. Get assessments with role-based filtering
                var result = _repository.Assessment.GetAssessmentsByRoleAsync(request.FundId.Value, request.Search, request.Type, request.Status, request.FromDate, request.ToDate, userRole.ToString(), false)
                            .OrderByDescending(x => x.UpdatedAt);

                if (!result.Any())
                {
                    return PaginatedResult<SingleAssessmentResponse>.EmptyCollection(_localizer[SharedResourcesKey.NoRecords]);
                }

                // 3. Project to DTO and paginate
                var AssessmentList = await _mapper.ProjectTo<SingleAssessmentResponse>(result).ToPaginatedListAsync(request.PageNumber, request.PageSize, "LastUpdated desc");

                // 4. Set action permissions for all assessments based on user role
                await SetActionPermissionsForList(AssessmentList.Data, currentUserId, userRole, request.FundId.GetValueOrDefault());
                                  
                _logger.LogInfo($"Retrieved {AssessmentList.Data.Count} assessments out of {AssessmentList.TotalCount} total");

                return AssessmentList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetAssessmentsList");
                return PaginatedResult<SingleAssessmentResponse>.ServerError(_localizer[SharedResourcesKey.AnErrorIsOccurredWhileSavingData]);
            }
        }

        /// <summary>
        /// Determines the current user's role within a specific fund context
        /// Checks FundBoardSecretary table, Fund.LegalCouncilId field, and FundManager table
        /// Follows Clean Architecture and CQRS patterns with proper repository usage
        /// Same implementation as Resolution ListQueryHandler
        /// </summary>
        /// <param name="fundId">The fund ID to check roles for</param>
        /// <param name="currentUserId">The current user ID to check roles for</param>
        /// <returns>User role in the fund, or None if no roles</returns>
        private async Task<Roles> GetUserFundRole(int fundId, int currentUserId)
        {
            try
            {
                _logger.LogInfo($"Checking fund roles for User ID: {currentUserId} in Fund ID: {fundId}");

                var userRole = Roles.None;
                // Get fund details with all related entities
                var fundDetails = await _repository.Funds.ViewFundUsers(fundId, trackChanges: false);
                if (fundDetails == null)
                {
                    _logger.LogWarn($"Fund not found with ID: {fundId}");
                    return Roles.None;
                }

                // 1. Check if user is Legal Council for the fund
                if (fundDetails.LegalCouncilId == currentUserId)
                {
                    userRole = Roles.LegalCouncil;
                    _logger.LogInfo($"User ID: {currentUserId} is Legal Council for Fund ID: {fundId}");
                }

                // 2. Check if user is a Fund Manager for the fund
                if (fundDetails.FundManagers != null && fundDetails.FundManagers.Count > 0)
                {
                    var isFundManager = fundDetails.FundManagers.Any(fm => fm.UserId == currentUserId);
                    if (isFundManager)
                    {
                        userRole = Roles.FundManager;
                        _logger.LogInfo($"User ID: {currentUserId} is Fund Manager for Fund ID: {fundId}");
                    }
                }

                // 3. Check if user is a Board Secretary for the fund
                if (fundDetails.FundBoardSecretaries != null && fundDetails.FundBoardSecretaries.Count > 0)
                {
                    var isBoardSecretary = fundDetails.FundBoardSecretaries.Any(bs => bs.UserId == currentUserId);
                    if (isBoardSecretary)
                    {
                        userRole = Roles.BoardSecretary;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Secretary for Fund ID: {fundId}");
                    }
                }
                // 3. Check if user is a Board Secretary for the fund
                if (fundDetails.BoardMembers != null && fundDetails.BoardMembers.Count > 0)
                {
                    var isMember = fundDetails.BoardMembers.Any(bs => bs.UserId == currentUserId);
                    if (isMember)
                    {
                        userRole = Roles.BoardMember;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Member for Fund ID: {fundId}");
                    }
                }
                _logger.LogInfo($"User ID: {currentUserId} has roles in Fund ID: {fundId}: '{userRole}'");

                return userRole;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking fund roles for User ID: {currentUserId} in Fund ID: {fundId}");
                return Roles.None;
            }
        }

        /// <summary>
        /// Sets action permissions for all assessments in the list based on user role and assessment status
        /// Optimized to minimize database calls by grouping assessments by fund
        /// </summary>
        /// <param name="assessments">List of assessment DTOs to set permissions for</param>
        /// <param name="userRole">Current user's role</param>
        /// <param name="currentUserId">Current user's ID</param>
        /// <param name="fundId">Fund ID</param>
        private async Task SetActionPermissionsForList(IEnumerable<SingleAssessmentResponse> assessments, int? currentUserId, Roles userFundRole, int fundId)
        {
            if (!assessments.Any() || !currentUserId.HasValue) return;

            foreach (var assessment in assessments)
            {
                await SetActionPermissionsAsync(assessment, userFundRole, fundId, assessment.CreatorID);
            }
        }

        /// <summary>
        /// Sets action permissions based on user role and assessment status
        /// Based on Assessment requirements for user stories and business rules
        /// Follows the same pattern as Resolution permissions
        /// </summary>
        private async Task SetActionPermissionsAsync(SingleAssessmentResponse response, Roles userRole, int fundId, int creatorID)
        {
            var isFundManager = userRole == Roles.FundManager;
            var isAssessmentOwner = userRole == Roles.FundManager;
            var isLegalCouncilOrBoardSecretary = userRole == Roles.LegalCouncil || userRole == Roles.BoardSecretary;

            // Default all actions to false
            
            response.CanEdit = false;
            response.CanDelete = false;
            

            // Set permissions based on status and role
            switch (response.Status)
            {
                case AssessmentStatus.Draft:
                    response.CanEdit = isFundManager && _currentUserService.UserId == creatorID;
                    response.CanDelete = _currentUserService.UserId == creatorID && isFundManager;
                    break;

                case AssessmentStatus.WaitingForApproval:
                    response.CanEdit = isFundManager;
                    break;

                case AssessmentStatus.Rejected:
                    response.CanEdit = isFundManager;
                    break;
            }
        }

        #endregion
       
    }
}
