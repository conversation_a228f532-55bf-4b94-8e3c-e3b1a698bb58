using Microsoft.Extensions.Localization;
using Resources;
using Application.Features.AssessmentManagement.Commands.ApproveAssessment;
using Abstraction.Contracts.Repository;
using Application.Features.AssessmentManagement.Commands.RejectAssessment;
using FluentValidation;

namespace Application.Features.AssessmentManagement.Validation
{
    /// <summary>
    /// Validation rules for ApproveAssessmentCommand
    /// Based on User Story 2: Approve or Reject Assessment requirements
    /// Implements validation for assessment approval
    /// Follows the same pattern as other approval validations for consistency
    /// </summary>
    public class ApproveAssessmentValidation : AbstractValidator<ApproveAssessmentCommand>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public ApproveAssessmentValidation(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;

            ApplyValidationRules();
        }
        private void ApplyValidationRules()
        {

            RuleFor(x => x.Id)
                .GreaterThan(0)
                .WithMessage(_localizer[SharedResourcesKey.InvalidIdValidation]);
        }
    }
}
