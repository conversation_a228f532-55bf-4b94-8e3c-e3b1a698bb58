using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;
using Application.Features.AssessmentManagement.Dtos;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Features.AssessmentManagement.Validation
{
    /// <summary>
    /// Base validation class for Assessment operations
    /// Contains common validation rules shared across different assessment operations
    /// Based on ValidationConstants and business requirements
    /// Follows the same pattern as Resolution BaseValidation for consistency
    /// </summary>
    public abstract class BaseAssessmentValidation<T> : AbstractValidator<T> where T : class
    {
        protected readonly IStringLocalizer<SharedResources> _localizer;

        protected BaseAssessmentValidation(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
            //ApplyValidationRules();
        }

        //#region Common Assessment Validation Rules
        //private void ApplyValidationRules()
        //{

        //}
        ///// <summary>
        ///// Validates assessment title
        ///// Arabic: التحقق من صحة عنوان التقييم
        ///// </summary>
        //protected void ValidateAssessmentTitle<TProperty>(Func<T, TProperty> expression) where TProperty : class
        //{
        //    RuleFor<T, TProperty>(expression)
        //        .NotEmpty()
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.AssessmentTitleRequired])
        //        .Length(ValidationConstants.AssessmentTitleMinLength, ValidationConstants.AssessmentTitleMaxLength)
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.AssessmentTitleTooShort])
        //        .When(x => !string.IsNullOrEmpty(expression(x)?.ToString()));
        //}

        ///// <summary>
        ///// Validates assessment description
        ///// Arabic: التحقق من صحة وصف التقييم
        ///// </summary>
        //protected void ValidateAssessmentDescription<TProperty>(Func<T, TProperty> expression) where TProperty : class
        //{
        //    RuleFor<T, TProperty>(expression)
        //        .MaximumLength(ValidationConstants.AssessmentDescriptionMaxLength)
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.AssessmentDescriptionTooLong])
        //        .When(x => !string.IsNullOrEmpty(expression(x)?.ToString()));
        //}

        ///// <summary>
        ///// Validates assessment instructions
        ///// Arabic: التحقق من صحة تعليمات التقييم
        ///// </summary>
        //protected void ValidateAssessmentInstructions<TProperty>(Func<T, TProperty> expression) where TProperty : class
        //{
        //    RuleFor<T, TProperty>(expression)
        //        .MaximumLength(ValidationConstants.AssessmentInstructionsMaxLength)
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.AssessmentInstructionsTooLong])
        //        .When(x => !string.IsNullOrEmpty(expression(x)?.ToString()));
        //}

        ///// <summary>
        ///// Validates assessment type
        ///// Arabic: التحقق من صحة نوع التقييم
        ///// </summary>
        //protected void ValidateAssessmentType(Func<T, AssessmentType> expression)
        //{
        //    RuleFor<T, AssessmentType>(expression)
        //        .IsInEnum()
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.AssessmentTypeRequired]);
        //}

        ///// <summary>
        ///// Validates fund ID
        ///// Arabic: التحقق من صحة معرف الصندوق
        ///// </summary>
        //protected void ValidateFundId(Func<T, int> expression)
        //{
        //    RuleFor<T, int>(expression)
        //        .GreaterThan(0)
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.FundIdRequired]);
        //}



        ///// <summary>
        ///// Validates assessment ID
        ///// Arabic: التحقق من صحة معرف التقييم
        ///// </summary>
        //protected void ValidateAssessmentId(Func<T, int> expression)
        //{
        //    RuleFor<T, int>(expression)
        //        .GreaterThan(0)
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.AssessmentIdRequired]);
        //}

        //#endregion

        //#region Question Validation Rules

        ///// <summary>
        ///// Validates assessment questions
        ///// Arabic: التحقق من صحة أسئلة التقييم
        ///// </summary>
        //protected void ValidateAssessmentQuestions<TQuestion>(
        //    Func<T, List<TQuestion>?> questionsExpression,
        //    Func<T, AssessmentType> typeExpression)
        //    where TQuestion : CreateAssessmentQuestionDto
        //{
        //    // Questionnaire type must have questions
        //    RuleFor<T, List<TQuestion>?>(questionsExpression)
        //        .NotEmpty()
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.QuestionnaireRequiresQuestions])
        //        .When(x => typeExpression(x) == AssessmentType.Questionnaire);

        //    // Maximum questions limit
        //    RuleFor<T, List<TQuestion>?>(questionsExpression)
        //        .Must(questions => questions == null || questions.Count <= ValidationConstants.MaxQuestionsPerAssessment)
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.TooManyQuestions]);

        //    // Validate individual questions
        //    RuleForEach<T, TQuestion>(questionsExpression)
        //        .SetValidator(new QuestionValidator(_localizer))
        //        .When(x => questionsExpression(x) != null);

        //    // Validate question order uniqueness
        //    RuleFor<T, List<TQuestion>?>(questionsExpression)
        //        .Must(questions => questions == null ||
        //             questions.GroupBy(q => q.Order).All(g => g.Count() == 1))
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.DuplicateQuestionOrder])
        //        .When(x => questionsExpression(x) != null);
        //}

        ///// <summary>
        ///// Validates assessment attachments
        ///// Arabic: التحقق من صحة مرفقات التقييم
        ///// </summary>
        //protected void ValidateAssessmentAttachments(Func<T, int?> attachmentIdExpression, Func<T, AssessmentType> typeExpression)
        //{
        //    // Attachment type should have attachment ID
        //    RuleFor<T, int?>(attachmentIdExpression)
        //        .NotNull()
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.AttachmentTypeRequiresAttachments])
        //        .When(x => typeExpression(x) == AssessmentType.Attachment);

        //    // Validate attachment ID is valid
        //    RuleFor<T, int?>(attachmentIdExpression)
        //        .GreaterThan(0)
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.InvalidAssessmentId])
        //        .When(x => attachmentIdExpression(x).HasValue);
        //}

        //#endregion

        //#region Action Validation Rules

        ///// <summary>
        ///// Validates rejection reason
        ///// Arabic: التحقق من صحة سبب الرفض
        ///// </summary>
        //protected void ValidateRejectionReason<TProperty>(Func<T, TProperty> expression) where TProperty : class
        //{
        //    RuleFor<T, TProperty>(expression)
        //        .NotEmpty()
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.RejectionReasonRequired])
        //        .Length(ValidationConstants.RejectionReasonMinLength, ValidationConstants.RejectionReasonMaxLength)
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.RejectionReasonTooShort])
        //        .When(x => !string.IsNullOrEmpty(expression(x)?.ToString()));
        //}

        ///// <summary>
        ///// Validates action comments
        ///// Arabic: التحقق من صحة تعليقات الإجراء
        ///// </summary>
        //protected void ValidateActionComments<TProperty>(Func<T, TProperty> expression) where TProperty : class
        //{
        //    RuleFor<T, TProperty>(expression)
        //        .MaximumLength(ValidationConstants.ActionCommentsMaxLength)
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.ActionCommentsTooLong])
        //        .When(x => !string.IsNullOrEmpty(expression(x)?.ToString()));
        //}

        //#endregion

        //#region Response Validation Rules

        ///// <summary>
        ///// Validates response comments
        ///// Arabic: التحقق من صحة تعليقات الرد
        ///// </summary>
        //protected void ValidateResponseComments<TProperty>(Func<T, TProperty> expression) where TProperty : class
        //{
        //    RuleFor<T, TProperty>(expression)
        //        .MaximumLength(ValidationConstants.ResponseCommentsMaxLength)
        //        .WithMessage(_localizer[ValidationConstants.ErrorMessages.ResponseCommentsTooLong])
        //        .When(x => !string.IsNullOrEmpty(expression(x)?.ToString()));
        //}

        //#endregion
    }

    #region Individual Validators

    /// <summary>
    /// Validator for assessment questions
    /// Arabic: مدقق أسئلة التقييم
    /// </summary>
    public class QuestionValidator : AbstractValidator<CreateAssessmentQuestionDto>
    {
        public QuestionValidator(IStringLocalizer<SharedResources> localizer)
        {
            RuleFor(x => x.QuestionText)
                .NotEmpty()
                .WithMessage(localizer[ValidationConstants.ErrorMessages.QuestionTextRequired])
                .Length(ValidationConstants.QuestionTextMinLength, ValidationConstants.QuestionTextMaxLength)
                .WithMessage(localizer[ValidationConstants.ErrorMessages.QuestionTextTooShort]);

            RuleFor(x => x.Type)
                .IsInEnum()
                .WithMessage(localizer[ValidationConstants.ErrorMessages.QuestionTypeRequired]);

            RuleFor(x => x.Order)
                .GreaterThan(0)
                .WithMessage(localizer[ValidationConstants.ErrorMessages.InvalidQuestionOrder]);

            // Choice questions must have options
            RuleFor(x => x.Options)
                .NotEmpty()
                .WithMessage(localizer[ValidationConstants.ErrorMessages.ChoiceQuestionRequiresOptions])
                .When(x => x.Type == QuestionType.SingleChoice || x.Type == QuestionType.MultiChoice);

            // Minimum options for choice questions
            RuleFor(x => x.Options)
                .Must(options => options == null || options.Count >= ValidationConstants.MinOptionsForChoiceQuestion)
                .WithMessage(localizer[ValidationConstants.ErrorMessages.TooFewOptionsForChoiceQuestion])
                .When(x => x.Type == QuestionType.SingleChoice || x.Type == QuestionType.MultiChoice);

            // Maximum options limit
            RuleFor(x => x.Options)
                .Must(options => options == null || options.Count <= ValidationConstants.MaxOptionsPerQuestion)
                .WithMessage(localizer[ValidationConstants.ErrorMessages.TooManyOptionsPerQuestion]);

            // Validate individual options
            RuleForEach(x => x.Options)
                .SetValidator(new OptionValidator(localizer))
                .When(x => x.Options != null);

            // Validate option order uniqueness
            RuleFor(x => x.Options)
                .Must(options => options == null || 
                     options.GroupBy(o => o.Order).All(g => g.Count() == 1))
                .WithMessage(localizer[ValidationConstants.ErrorMessages.DuplicateOptionOrder])
                .When(x => x.Options != null);
        }
    }

    /// <summary>
    /// Validator for question options
    /// Arabic: مدقق خيارات الأسئلة
    /// </summary>
    public class OptionValidator : AbstractValidator<CreateAssessmentOptionDto>
    {
        public OptionValidator(IStringLocalizer<SharedResources> localizer)
        {
            RuleFor(x => x.OptionText)
                .NotEmpty()
                .WithMessage(localizer[ValidationConstants.ErrorMessages.OptionTextRequired])
                .MaximumLength(ValidationConstants.OptionTextMaxLength)
                .WithMessage(localizer[ValidationConstants.ErrorMessages.OptionTextTooLong]);

            RuleFor(x => x.Order)
                .GreaterThan(0)
                .WithMessage(localizer[ValidationConstants.ErrorMessages.InvalidOptionOrder]);
        }
    }

    /// <summary>
    /// Validator for assessment attachments
    /// Arabic: مدقق مرفقات التقييم
    /// </summary>
    public class AttachmentValidator : AbstractValidator<CreateAssessmentAttachmentDto>
    {
        public AttachmentValidator(IStringLocalizer<SharedResources> localizer)
        {
            RuleFor(x => x.AttachmentId)
                .GreaterThan(0)
                .WithMessage(localizer[ValidationConstants.ErrorMessages.InvalidAssessmentId]);

            RuleFor(x => x.Description)
                .MaximumLength(ValidationConstants.AttachmentDescriptionMaxLength)
                .WithMessage(localizer[ValidationConstants.ErrorMessages.AttachmentDescriptionTooLong])
                .When(x => !string.IsNullOrEmpty(x.Description));

            RuleFor(x => x.Order)
                .GreaterThan(0)
                .WithMessage(localizer[ValidationConstants.ErrorMessages.InvalidOptionOrder]);
        }
    }

    #endregion
}
