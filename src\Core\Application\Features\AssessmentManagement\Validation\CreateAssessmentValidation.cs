using Abstraction.Contracts.Repository;
using Application.Features.AssessmentManagement.Commands.CreateAssessment;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Entities.FundManagement;
using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;

namespace Application.Features.AssessmentManagement.Validation
{
    /// <summary>
    /// Validation rules for CreateAssessmentCommand
    /// Based on User Story 1: Create New Assessment requirements
    /// Implements comprehensive validation for assessment creation
    /// Follows the same pattern as AddResolutionValidation for consistency
    /// </summary>
    public class CreateAssessmentValidation : AbstractValidator<CreateAssessmentCommand>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly IRepositoryManager _repository;

        public CreateAssessmentValidation(IStringLocalizer<SharedResources> localizer, IRepositoryManager repository)
        {
            _localizer = localizer;
            _repository = repository;

            ApplyValidationRules();
        }
        private void ApplyValidationRules()
        {

            RuleFor(x => x.FundId)
                .GreaterThan(0)
                .WithMessage(_localizer[SharedResourcesKey.InvalidIdValidation]);

            RuleFor(x => x.Title)
                .NotEmpty()
                .WithMessage(_localizer[SharedResourcesKey.Required]);

            RuleFor(x => x.FundId)
                .MustAsync(async (fundId, cancellation) =>
                {
                    var fund = await _repository.Funds.GetByIdAsync<Fund>(fundId, trackChanges: false);
                    return fund != null;
                })
                .WithMessage(_localizer[SharedResourcesKey.FundNotFound]);

            // Business rule: Attachment must exist if provided
             RuleFor(x =>x.AttachmentId)
                .NotEmpty()
                .When(x =>x.Type == AssessmentType.Attachment && !x.SaveAsDraft)
                .WithMessage(_localizer[SharedResourcesKey.AttachmentNotFound]);

            
            // Business rule: Attachment must exist if provided
             RuleFor(x =>x.Questions)
                .NotEmpty()
                .When(x =>x.Type == AssessmentType.Questionnaire && !x.SaveAsDraft)
                .WithMessage(_localizer[SharedResourcesKey.AttachmentNotFound]);

            
        }
    }
}
