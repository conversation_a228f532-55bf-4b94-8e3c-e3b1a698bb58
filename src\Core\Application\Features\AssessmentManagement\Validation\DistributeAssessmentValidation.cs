using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;
using Application.Features.AssessmentManagement.Commands.DistributeAssessment;
using Application.Features.AssessmentManagement.Commands.ApproveAssessment;

namespace Application.Features.AssessmentManagement.Validation
{
    /// <summary>
    /// Validation rules for DistributeAssessmentCommand
    /// Based on User Story 3: Distribute Assessment requirements
    /// Implements validation for assessment distribution to board members
    /// Follows the same pattern as other distribution validations for consistency
    /// </summary>
    public class DistributeAssessmentValidation : AbstractValidator<DistributeAssessmentCommand>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public DistributeAssessmentValidation(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;

            ApplyValidationRules();
        }
        private void ApplyValidationRules()
        {

            RuleFor(x => x.Id)
                .GreaterThan(0)
                .WithMessage(_localizer[SharedResourcesKey.InvalidIdValidation]);
        }
    }
}
