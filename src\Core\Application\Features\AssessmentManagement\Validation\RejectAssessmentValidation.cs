using Microsoft.Extensions.Localization;
using Resources;
using Application.Features.AssessmentManagement.Commands.RejectAssessment;
using Abstraction.Contracts.Repository;
using FluentValidation;

namespace Application.Features.AssessmentManagement.Validation
{
    /// <summary>
    /// Validation rules for RejectAssessmentCommand
    /// Based on User Story 2: Approve or Reject Assessment requirements
    /// Implements validation for assessment rejection with mandatory reason
    /// Follows the same pattern as RejectResolutionValidation for consistency
    /// </summary>
    public class RejectAssessmentValidation : AbstractValidator<RejectAssessmentCommand>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly IRepositoryManager _repository;

        public RejectAssessmentValidation(IStringLocalizer<SharedResources> localizer, IRepositoryManager repository)
        {
            _localizer = localizer;
            _repository = repository;

            ApplyValidationRules();
        }
        private void ApplyValidationRules()
        {

            RuleFor(x => x.Id)
                .GreaterThan(0)
                .WithMessage(_localizer[SharedResourcesKey.InvalidIdValidation]);

            RuleFor(x => x.RejectionReason)
                .NotEmpty()
                .WithMessage(_localizer[SharedResourcesKey.Required]);
        }
    }
}