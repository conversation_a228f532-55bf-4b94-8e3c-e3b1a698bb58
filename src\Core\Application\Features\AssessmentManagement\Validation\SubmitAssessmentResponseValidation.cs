using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;
using Application.Features.AssessmentManagement.Commands.SubmitAssessmentResponse;
using Application.Features.AssessmentManagement.Dtos;

namespace Application.Features.AssessmentManagement.Validation
{
    /// <summary>
    /// Validation rules for SubmitAssessmentResponseCommand
    /// Based on User Story 4: Respond to Assessment requirements
    /// Implements comprehensive validation for board member responses
    /// Follows the same pattern as other response validations for consistency
    /// </summary>
    public class SubmitAssessmentResponseValidation : BaseAssessmentValidation<SubmitAssessmentResponseCommand>
    {
        public SubmitAssessmentResponseValidation(IStringLocalizer<SharedResources> localizer) : base(localizer)
        {
            // Validate assessment ID
            RuleFor(x => x.AssessmentId)
                .GreaterThan(0)
                .WithMessage(_localizer[ValidationConstants.ErrorMessages.AssessmentIdRequired]);

            // Validate answers if provided
            RuleFor(x => x.Answers)
                .NotEmpty()
                .WithMessage(_localizer[ValidationConstants.ErrorMessages.AnswersRequired])
                .When(x => !x.SaveAsDraft && x.AssessmentType == Domain.Entities.AssessmentManagement.Enums.AssessmentType.Questionnaire); // Answers required for final submission

            // Validate individual answers
            RuleForEach(x => x.Answers)
                .SetValidator(new AnswerValidator(_localizer))
                .When(x => x.Answers != null && !x.SaveAsDraft && x.AssessmentType == Domain.Entities.AssessmentManagement.Enums.AssessmentType.Questionnaire);
        }
    }

    /// <summary>
    /// Validator for individual assessment answers
    /// Arabic: مدقق إجابات التقييم الفردية
    /// </summary>
    public class AnswerValidator : AbstractValidator<SubmitAssessmentAnswerDto>
    {
        public AnswerValidator(IStringLocalizer<SharedResources> localizer)
        {
            RuleFor(x => x.QuestionId)
                .GreaterThan(0)
                .WithMessage(localizer[ValidationConstants.ErrorMessages.InvalidQuestionId]);

            // Text answer validation
            RuleFor(x => x.TextAnswer)
                .MaximumLength(ValidationConstants.TextAnswerMaxLength)
                .WithMessage(localizer[ValidationConstants.ErrorMessages.TextAnswerTooLong])
                .When(x => !string.IsNullOrEmpty(x.TextAnswer));

            // Selected option IDs validation
            RuleForEach(x => x.SelectedOptionIds)
                .GreaterThan(0)
                .WithMessage(localizer["InvalidOptionId"])
                .When(x => x.SelectedOptionIds != null);

            // Validate that option IDs are unique
            RuleFor(x => x.SelectedOptionIds)
                .Must(ids => ids == null || ids.Distinct().Count() == ids.Count)
                .WithMessage(localizer["DuplicateOptionIds"])
                .When(x => x.SelectedOptionIds != null);

            // At least one answer type should be provided
            RuleFor(x => x)
                .Must(answer => !string.IsNullOrEmpty(answer.TextAnswer) ||
                              (answer.SelectedOptionIds != null && answer.SelectedOptionIds.Any()))
                .WithMessage(localizer[ValidationConstants.ErrorMessages.RequiredQuestionNotAnswered]);
        }
    }
}
