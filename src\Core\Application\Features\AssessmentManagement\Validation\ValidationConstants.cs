namespace Application.Features.AssessmentManagement.Validation
{
    /// <summary>
    /// Constants for Assessment validation rules
    /// Contains validation limits and constraints for assessment-related operations
    /// Based on business requirements and database constraints
    /// Follows the same pattern as Resolution ValidationConstants for consistency
    /// </summary>
    public static class ValidationConstants
    {
        #region Assessment Validation Constants

        /// <summary>
        /// Minimum length for assessment title
        /// Arabic: الحد الأدنى لطول عنوان التقييم
        /// </summary>
        public const int AssessmentTitleMinLength = 3;

        /// <summary>
        /// Maximum length for assessment title
        /// Arabic: الحد الأقصى لطول عنوان التقييم
        /// </summary>
        public const int AssessmentTitleMaxLength = 200;

        /// <summary>
        /// Maximum length for assessment description
        /// Arabic: الحد الأقصى لطول وصف التقييم
        /// </summary>
        public const int AssessmentDescriptionMaxLength = 2000;

        /// <summary>
        /// Maximum length for assessment instructions
        /// Arabic: الحد الأقصى لطول تعليمات التقييم
        /// </summary>
        public const int AssessmentInstructionsMaxLength = 1000;

        /// <summary>
        /// Maximum number of questions per assessment
        /// Arabic: العدد الأقصى للأسئلة في التقييم
        /// </summary>
        public const int MaxQuestionsPerAssessment = 50;

        /// <summary>
        /// Maximum number of attachments per assessment
        /// Arabic: العدد الأقصى للمرفقات في التقييم
        /// </summary>
        public const int MaxAttachmentsPerAssessment = 20;



        #endregion

        #region Question Validation Constants

        /// <summary>
        /// Minimum length for question text
        /// Arabic: الحد الأدنى لطول نص السؤال
        /// </summary>
        public const int QuestionTextMinLength = 5;

        /// <summary>
        /// Maximum length for question text
        /// Arabic: الحد الأقصى لطول نص السؤال
        /// </summary>
        public const int QuestionTextMaxLength = 500;

        /// <summary>
        /// Minimum number of options for choice-type questions
        /// Arabic: العدد الأدنى للخيارات في الأسئلة ذات الخيارات
        /// </summary>
        public const int MinOptionsForChoiceQuestion = 2;

        /// <summary>
        /// Maximum number of options per question
        /// Arabic: العدد الأقصى للخيارات في السؤال
        /// </summary>
        public const int MaxOptionsPerQuestion = 10;

        /// <summary>
        /// Minimum length for option text
        /// Arabic: الحد الأدنى لطول نص الخيار
        /// </summary>
        public const int OptionTextMinLength = 1;

        /// <summary>
        /// Maximum length for option text
        /// Arabic: الحد الأقصى لطول نص الخيار
        /// </summary>
        public const int OptionTextMaxLength = 200;

        #endregion

        #region Response Validation Constants

        /// <summary>
        /// Maximum length for response comments
        /// Arabic: الحد الأقصى لطول تعليقات الرد
        /// </summary>
        public const int ResponseCommentsMaxLength = 1000;

        /// <summary>
        /// Maximum length for text answer
        /// Arabic: الحد الأقصى لطول الإجابة النصية
        /// </summary>
        public const int TextAnswerMaxLength = 2000;

        /// <summary>
        /// Maximum number of response attachments
        /// Arabic: العدد الأقصى لمرفقات الرد
        /// </summary>
        public const int MaxResponseAttachments = 10;

        #endregion

        #region Attachment Validation Constants

        /// <summary>
        /// Maximum length for attachment description
        /// Arabic: الحد الأقصى لطول وصف المرفق
        /// </summary>
        public const int AttachmentDescriptionMaxLength = 500;

        #endregion

        #region Action Validation Constants

        /// <summary>
        /// Maximum length for rejection reason
        /// Arabic: الحد الأقصى لطول سبب الرفض
        /// </summary>
        public const int RejectionReasonMaxLength = 1000;

        /// <summary>
        /// Minimum length for rejection reason
        /// Arabic: الحد الأدنى لطول سبب الرفض
        /// </summary>
        public const int RejectionReasonMinLength = 10;

        /// <summary>
        /// Maximum length for action comments
        /// Arabic: الحد الأقصى لطول تعليقات الإجراء
        /// </summary>
        public const int ActionCommentsMaxLength = 500;

        #endregion

        #region Business Rule Constants

        /// <summary>
        /// Minimum hours before due date to allow responses
        /// Arabic: الحد الأدنى للساعات قبل تاريخ الاستحقاق للسماح بالردود
        /// </summary>
        public const int MinHoursBeforeDueDate = 1;

        /// <summary>
        /// Maximum number of board members per assessment
        /// Arabic: العدد الأقصى لأعضاء مجلس الإدارة في التقييم
        /// </summary>
        public const int MaxBoardMembersPerAssessment = 50;

        /// <summary>
        /// Maximum number of edit attempts for responses
        /// Arabic: العدد الأقصى لمحاولات تعديل الردود
        /// </summary>
        public const int MaxResponseEditAttempts = 5;

        #endregion

        #region Error Messages Keys

        /// <summary>
        /// Error message keys for localization
        /// Arabic: مفاتيح رسائل الخطأ للترجمة
        /// </summary>
        public static class ErrorMessages
        {
            public const string AssessmentTitleRequired = "AssessmentTitleRequired";
            public const string AssessmentTitleTooShort = "AssessmentTitleTooShort";
            public const string AssessmentTitleTooLong = "AssessmentTitleTooLong";
            public const string AssessmentDescriptionTooLong = "AssessmentDescriptionTooLong";
            public const string AssessmentInstructionsTooLong = "AssessmentInstructionsTooLong";
            public const string AssessmentTypeRequired = "AssessmentTypeRequired";
            public const string FundIdRequired = "FundIdRequired";

            public const string TooManyQuestions = "TooManyQuestions";
            public const string TooManyAttachments = "TooManyAttachments";
            public const string QuestionnaireRequiresQuestions = "QuestionnaireRequiresQuestions";
            public const string AttachmentTypeRequiresAttachments = "AttachmentTypeRequiresAttachments";
            public const string QuestionTextRequired = "QuestionTextRequired";
            public const string QuestionTextTooShort = "QuestionTextTooShort";
            public const string QuestionTextTooLong = "QuestionTextTooLong";
            public const string QuestionTypeRequired = "QuestionTypeRequired";
            public const string ChoiceQuestionRequiresOptions = "ChoiceQuestionRequiresOptions";
            public const string TooFewOptionsForChoiceQuestion = "TooFewOptionsForChoiceQuestion";
            public const string TooManyOptionsPerQuestion = "TooManyOptionsPerQuestion";
            public const string OptionTextRequired = "OptionTextRequired";
            public const string OptionTextTooLong = "OptionTextTooLong";
            public const string RejectionReasonRequired = "RejectionReasonRequired";
            public const string RejectionReasonTooShort = "RejectionReasonTooShort";
            public const string RejectionReasonTooLong = "RejectionReasonTooLong";
            public const string ActionCommentsTooLong = "ActionCommentsTooLong";
            public const string ResponseCommentsTooLong = "ResponseCommentsTooLong";
            public const string TextAnswerTooLong = "TextAnswerTooLong";
            public const string TooManyResponseAttachments = "TooManyResponseAttachments";
            public const string AttachmentDescriptionTooLong = "AttachmentDescriptionTooLong";
            public const string InvalidQuestionOrder = "InvalidQuestionOrder";
            public const string InvalidOptionOrder = "InvalidOptionOrder";
            public const string DuplicateQuestionOrder = "DuplicateQuestionOrder";
            public const string DuplicateOptionOrder = "DuplicateOptionOrder";
            public const string AssessmentIdRequired = "AssessmentIdRequired";
            public const string InvalidAssessmentId = "InvalidAssessmentId";
            public const string AnswersRequired = "AnswersRequired";
            public const string InvalidQuestionId = "InvalidQuestionId";
            public const string RequiredQuestionNotAnswered = "RequiredQuestionNotAnswered";
            public const string InvalidOptionSelection = "InvalidOptionSelection";
            public const string MultipleAnswersForSingleChoice = "MultipleAnswersForSingleChoice";
            public const string NoAnswerForRequiredQuestion = "NoAnswerForRequiredQuestion";
        }

        #endregion
    }
}
