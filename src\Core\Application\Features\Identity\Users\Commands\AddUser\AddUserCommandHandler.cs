﻿using AutoMapper;
using Application.Base.Abstracts;
using Domain.Entities.Users;
using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contract.Service;
using Abstraction.Constants;
using Abstraction.Contracts.Logger;
using Core.Abstraction.Contract.Service.Notifications;
using System.Linq;
using System.Text;
using Abstraction.Contracts.Repository;
using Domain.Entities.FundManagement;
using System;


namespace Application.Features.Identity.Users.Commands.AddUser
{
    /// <summary>
    /// Enhanced handler for adding users with Sprint 3 administrative features
    /// Includes role assignment and registration message integration
    /// </summary>
    public class AddUserCommandHandler : BaseResponseHandler, ICommandHandler<AddUserCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IIdentityServiceManager _identityServiceManager;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        private readonly IWhatsAppNotificationService _whatsAppService;
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        // TODO: Add IFileUploadService when available
        #endregion

        #region Constructors
        public AddUserCommandHandler(
            IIdentityServiceManager identityServiceManager,
            IMapper mapper,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService,
            IWhatsAppNotificationService whatsAppService,
            ILoggerManager logger,
            IRepositoryManager repository)
        {
            _mapper = mapper;
            _identityServiceManager = identityServiceManager;
            _localizer = localizer;
            _currentUserService = currentUserService;
            _whatsAppService = whatsAppService;
            _logger = logger;
            _repository = repository;
        }
        #endregion

        #region Private Methods
        public async Task<BaseResponse<string>> Handle(AddUserCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Check if user exists by email
                var existingUserByEmail = await _identityServiceManager.UserManagmentService.FindByEmailAsync(request.Email);
                if (existingUserByEmail != null)
                    return BadRequest<string>(_localizer[SharedResourcesKey.ProfileDuplicateEmail]);

                // Check if user exists by username
                var existingUserByUserName = await _identityServiceManager.UserManagmentService.FindByNameAsync(request.UserName);
                if (existingUserByUserName != null)
                    return BadRequest<string>(_localizer[SharedResourcesKey.UsernameAlreadyInUse]);

                request.CvFileId = request.CvFileId != 0 ? request.CvFileId : null;
                // Map to user entity
                var user = _mapper.Map<User>(request);
                var password = GenerateTemporaryPassword();
                // Set Sprint 3 specific fields
                user.CountryCode = "+20";
                user.PreferredLanguage = "ar-EG"; // Default to Arabic
                user.PhoneNumber = request.UserName;
 
                user.RegistrationMessageIsSent = request.Roles.All(c => c == "Board Member") ? false : true;
                user.RegistrationIsCompleted = false;
                user.CreatedAt = DateTime.Now;
                user.CreatedBy = _currentUserService.UserId;
                // Create user
                var result = await _identityServiceManager.UserManagmentService.CreateAsync(user, password);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return BadRequest<string>($"{_localizer[SharedResourcesKey.SystemErrorSavingData]}: {errors}");
                }

                // Assign roles
                var assignedRoles = new List<string>();
                if (request.Roles.Any())
                {
                    foreach (var role in request.Roles)
                    {
                        var roleExists = await _identityServiceManager.AuthorizationService.IsRoleNameExist(role);
                        if (roleExists)
                        {
                            await _identityServiceManager.UserManagmentService.AddToRoleAsync(user, role);
                            assignedRoles.Add(role);
                        }
                    }
                }
                else
                {
                    // Default role assignment
                    var defaultRoleExists = await _identityServiceManager.AuthorizationService.IsRoleNameExist("USER");
                    if (!defaultRoleExists)
                    {
                        await _identityServiceManager.AuthorizationService.AddRoleAsync("USER", null);
                    }
                    await _identityServiceManager.UserManagmentService.AddToRoleAsync(user, "USER");
                    assignedRoles.Add("USER");
                }
                if (request.Roles.Contains(RoleHelper.LegalCouncil) || request.Roles.Contains(RoleHelper.HeadOfRealEstate) || request.Roles.Contains(RoleHelper.FinanceController) || request.Roles.Contains(RoleHelper.ComplianceLegalManagingDirector))
                {
                    foreach (var role in request.Roles)
                    {
                        var updatedUser = await _identityServiceManager.UserManagmentService.FindActiveUserWithOnlyRoleAsync(role, user.Id);
                        if(updatedUser!=null )
                        {
                            updatedUser.IsActive = false;
                            await _identityServiceManager.UserManagmentService.UpdateAsync(updatedUser);
                        }
                        //if (role == RoleHelper.LegalCouncil)
                        //{
                        //    UpdateRelatedFunds(updatedUser, user);
                        //}
                    }


                }

                if (request.Roles.Contains(RoleHelper.LegalCouncil))
                {
                    var legalCouncilUser = await _identityServiceManager.UserManagmentService.FindActiveUserWithOnlyRoleAsync(RoleHelper.LegalCouncil, user.Id);
                    var funds =  _repository.Funds.GetByCondition<Fund>(f => f.LegalCouncilId == legalCouncilUser.Id);
                    foreach (var fund in funds)
                    {
                        fund.LegalCouncilId = user.Id;
                    }
                    await _repository.Funds.UpdateRangeAsync(funds.ToList());
                }
                // Prepare response


                // Send registration message if eligible (not only Board Member)
                if (user.RegistrationMessageIsSent)
                {
                    await SendWhatsAppRegistrationNotificationAsync(user, assignedRoles, password);
                }

                return Success<string>(_localizer[SharedResourcesKey.UserAddedSuccessfully]);
            }
            catch (Exception ex)
            {
                return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorSavingData]);
            }
        }

        /// <summary>
        /// Sends WhatsApp registration notification to newly created user
        /// Implements MSG-ADD-008 from Sprint 3 requirements
        /// Only sends if user is eligible (not only Board Member role)
        /// </summary>
        private async Task SendWhatsAppRegistrationNotificationAsync(User user, List<string> userRoles, string temporaryPassword)
        {
            try
            {
                _logger.LogInfo($"Sending WhatsApp registration notification to user {user.Id}");

                // Format phone number for WhatsApp
                var phoneNumber =string.Format("{0}{1}", user.CountryCode , user.UserName);
                if (string.IsNullOrEmpty(phoneNumber))
                {
                    _logger.LogWarn($"Invalid phone number for user {user.Id}. WhatsApp notification skipped.");
                    return;
                }

                // Get primary role for message
                var primaryRole = userRoles.FirstOrDefault() ?? "User";
                

                // Send WhatsApp registration notification
                var response = await _whatsAppService.SendUserRegistrationMessageAsync(
                    user.Id,
                    phoneNumber,
                    temporaryPassword,
                    
                    CancellationToken.None);

                if (response.IsSuccess)
                {
                    _logger.LogInfo($"WhatsApp registration notification sent successfully to user {user.Id}. MessageId: {response.MessageId}");
                }
                else
                {
                    _logger.LogWarn($"WhatsApp registration notification failed for user {user.Id}. Error: {response.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                // Log WhatsApp failure but don't fail the entire operation (MSG-ADD-009)
                _logger.LogError(ex, $"Failed to send WhatsApp registration notification to user {user.Id}");
            }
        }

     
         
        /// <summary>
        /// Generate a secure temporary password
        /// </summary>
        private string GenerateTemporaryPassword()
        {
            const string upperCase = "JADW";
            const string lowerCase = "jadw";
            const string digits = "0123456789";
            const string specialChars = "@";

            var random = new Random();
            var password = new StringBuilder();

            // Ensure at least one character from each category
            password.Append(upperCase[random.Next(upperCase.Length)]);
            password.Append(lowerCase[random.Next(lowerCase.Length)]);
            password.Append(digits[random.Next(digits.Length)]);
            password.Append(specialChars[random.Next(specialChars.Length)]);

            // Fill the rest with random characters
            const string allChars = upperCase + lowerCase + digits + specialChars;
            for (int i = 4; i < 6; i++) // Total length of 12 characters
            {
                password.Append(allChars[random.Next(allChars.Length)]);
            }

            // Shuffle the password
            var passwordArray = password.ToString().ToCharArray();
            for (int i = passwordArray.Length - 1; i > 0; i--)
            {
                int j = random.Next(i + 1);
                (passwordArray[i], passwordArray[j]) = (passwordArray[j], passwordArray[i]);
            }

            return new string(passwordArray);
        }

        private async Task UpdateRelatedFunds(User oldUser, User newUSer)
        {
            var funds = _repository.Funds.GetByCondition<Fund>(f => f.LegalCouncilId == oldUser.Id,true);
            if (oldUser is null)
                funds = _repository.Funds.GetAll<Fund>(true);
            foreach (var fund in funds)
            {
                fund.LegalCouncilId = newUSer.Id;
            }
            await _repository.Funds.UpdateRangeAsync(funds.ToList());
        }
        #endregion
    }
}