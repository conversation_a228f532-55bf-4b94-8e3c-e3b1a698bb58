﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Abstraction.Base.Response;
using Application.Base.Abstracts;
using Application.Features.ResolutionMemberVotes.Dtos;

namespace Application.Features.ResolutionMemberVotes.Commands.AddItemComment
{
  
    public record ItemCommentCommand : ItemCommentRequest, ICommand<BaseResponse<string>>
    {

    }
}
