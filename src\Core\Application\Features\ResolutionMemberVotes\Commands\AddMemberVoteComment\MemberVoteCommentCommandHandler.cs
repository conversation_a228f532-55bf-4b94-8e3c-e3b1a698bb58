﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Abstraction.Base.Response;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using Application.Features.ResolutionMemberVotes.Commands.Edit;
using Application.Features.ResolutionMemberVotes.Dtos;
using AutoMapper;
using Domain.Entities.ResolutionManagement;
using Domain.Entities.FundManagement;
using Abstraction.Constants;

namespace Application.Features.ResolutionMemberVotes.Commands.AddItemComment
{
    public class MemberVoteCommentCommandHandler : BaseResponseHandler, ICommandHandler<MemberVoteCommentCommand, BaseResponse<string>>
    {

        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _Repository;
        private readonly IMapper _mapper;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructors
        public MemberVoteCommentCommandHandler(IRepositoryManager Repository, IMapper mapper, ILoggerManager logger, ICurrentUserService currentUserService)
        {
            _logger = logger;
            _Repository = Repository;
            _mapper = mapper;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Handle Functions
        public async Task<BaseResponse<string>> Handle(MemberVoteCommentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                if (request == null)
                    return BadRequest<string>("the request can't be blank");

                // Load ResolutionMemberVote to get the Resolution and Fund information
                var memberVote = await _Repository.ResolutionMemberVotes.GetByIdAsync<ResolutionMemberVote>(request.ResolutionMemberVoteId, false);
                if (memberVote == null)
                    return BadRequest<string>("Resolution member vote not found.");

                // Get the resolution to access fund information
                var resolution = await _Repository.Resolutions.GetByIdAsync<Resolution>(memberVote.ResolutionId, false);
                if (resolution == null)
                    return BadRequest<string>("Resolution not found.");

                // Load fund with all related entities for role checking
                var fund = await _Repository.Funds.ViewFundUsers(resolution.FundId, false);
                if (fund == null)
                    return BadRequest<string>("Fund not found.");

                var currentUserId = _currentUserService.UserId ?? 0;
                if (currentUserId == 0)
                    return BadRequest<string>("User not authenticated.");

                // Get user's role in the fund and validate access
                var userRoleResult = await GetUserFundRoleAndType(fund, currentUserId);
                var userRole = userRoleResult.userRole;
                var boardMemberType = userRoleResult.boardMemberType;

                if (userRole == Roles.None)
                    return BadRequest<string>("User does not have access to this fund.");

                // Create the comment with role metadata
                var memberVoteComment = new ResolutionMemberVoteComment
                {
                    Comment = request.Comment,
                    ResolutionMemberVoteID = request.ResolutionMemberVoteId,
                    UserRoleOrBoardMemberType = userRole == Roles.BoardMember ?
                        ((int)(boardMemberType ?? BoardMemberType.NotIndependent)).ToString() : userRole.ToString(),
                    IsBoardMemberComment = userRole == Roles.BoardMember
                };

                var addedComment = await _Repository.ResolutionMemberVoteComments.AddAsync(memberVoteComment);
                if (addedComment == null)
                    return BadRequest<string>("Add Operation Failed.");

                return Success("Comment added successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in MemberVoteCommentCommand");
                return ServerError<string>(ex.Message);
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Determines the current user's role within a specific fund context and board member type if applicable
        /// Checks FundBoardSecretary table, Fund.LegalCouncilId field, FundManager table, and BoardMembers table
        /// Follows the same pattern as used in CheckUserFundAccessQueryHandler
        /// </summary>
        /// <param name="fund">Fund entity with related entities loaded</param>
        /// <param name="currentUserId">Current user ID to check roles for</param>
        /// <returns>Tuple of user's role and board member type (if applicable)</returns>
        private async Task<(Roles userRole, BoardMemberType? boardMemberType)> GetUserFundRoleAndType(Fund fund, int currentUserId)
        {
            try
            {
                _logger.LogInfo($"Checking fund roles for User ID: {currentUserId} in Fund ID: {fund.Id}");

                // 1. Check if user is Legal Council for the fund
                if (fund.LegalCouncilId == currentUserId)
                {
                    _logger.LogInfo($"User ID: {currentUserId} is Legal Council for Fund ID: {fund.Id}");
                    return (Roles.LegalCouncil, null);
                }

                // 2. Check if user is a Fund Manager for the fund
                if (fund.FundManagers != null && fund.FundManagers.Count > 0)
                {
                    var isFundManager = fund.FundManagers.Any(fm => fm.UserId == currentUserId);
                    if (isFundManager)
                    {
                        _logger.LogInfo($"User ID: {currentUserId} is Fund Manager for Fund ID: {fund.Id}");
                        return (Roles.FundManager, null);
                    }
                }

                // 3. Check if user is a Board Secretary for the fund
                if (fund.FundBoardSecretaries != null && fund.FundBoardSecretaries.Count > 0)
                {
                    var isBoardSecretary = fund.FundBoardSecretaries.Any(bs => bs.UserId == currentUserId);
                    if (isBoardSecretary)
                    {
                        _logger.LogInfo($"User ID: {currentUserId} is Board Secretary for Fund ID: {fund.Id}");
                        return (Roles.BoardSecretary, null);
                    }
                }

                // 4. Check if user is a Board Member for the fund
                if (fund.BoardMembers != null && fund.BoardMembers.Count > 0)
                {
                    var boardMember = fund.BoardMembers.FirstOrDefault(bm => bm.UserId == currentUserId);
                    if (boardMember != null)
                    {
                        _logger.LogInfo($"User ID: {currentUserId} is Board Member for Fund ID: {fund.Id} with type: {boardMember.MemberType}");
                        return (Roles.BoardMember, boardMember.MemberType);
                    }
                }

                _logger.LogInfo($"User ID: {currentUserId} has no roles in Fund ID: {fund.Id}");
                return (Roles.None, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking fund roles for User ID: {currentUserId} in Fund ID: {fund.Id}");
                return (Roles.None, null);
            }
        }

        #endregion

    }
}
