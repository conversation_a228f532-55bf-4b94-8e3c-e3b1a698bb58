using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Domain.Entities.ResolutionManagement;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;
using Microsoft.Extensions.Localization;
using Resources;
using Domain.Entities.ResolutionManagement.Enums;

namespace Application.Features.ResolutionMemberVotes.Commands.RequestRevote
{
    /// <summary>
    /// Hand<PERSON> for requesting a revote on a resolution member vote
    /// Follows Clean Architecture principles and established CQRS patterns
    /// Updates ResolutionMemberVote status and creates history entry
    /// </summary>
    public class ApproveRevoteCommandHandler : BaseResponseHandler, ICommandHandler<ApproveRevoteCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IStringLocalizer<SharedResources> _localizer;
        #endregion

        #region Constructors
        public ApproveRevoteCommandHandler(
            IRepositoryManager repository,
            ILoggerManager logger,
            ICurrentUserService currentUserService,
            IStringLocalizer<SharedResources> localizer)
        {
            _logger = logger;
            _repository = repository;
            _currentUserService = currentUserService;
            _localizer = localizer;
        }
        #endregion

        #region Handle Functions
        public async Task<BaseResponse<string>> Handle(ApproveRevoteCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Validate request
                if (request == null)
                    return BadRequest<string>(_localizer[SharedResourcesKey.EmptyRequestValidation]);

                if (request.ResolutionMemberVoteId <= 0)
                    return BadRequest<string>(_localizer[SharedResourcesKey.EmptyIdValidation]);

                // Get the ResolutionMemberVote entity with tracking enabled for updates
                var memberVote = await _repository.ResolutionMemberVotes.GetByIdIncludedItemVotesAsync(
                    request.ResolutionMemberVoteId, 
                    trackChanges: true);

                 
                if (memberVote == null)
                    return NotFound<string>(_localizer["ResolutionMemberVoteNotFound"]);

                // Validate that the current user has permission to request revote
                // This could be enhanced with specific business rules if needed
                var currentUserId = _currentUserService.UserId;
                if (!currentUserId.HasValue)
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);

                // Create a new entry in ResolutionMemberVoteStatusHistory to track this status change
                // Following the established pattern from EditResolutionMemberVoteCommandHandler
                memberVote.ResolutionMemberVoteStatusHistories.Add(new ResolutionMemberVoteStatusHistory
                {
                    StatusID = request.IsApproved ?  (int) ResolutionMemberVoteStatusEnum.NotVotedYet : (int)ResolutionMemberVoteStatusEnum.Voted,
                    ResolutionMemberVoteID = request.ResolutionMemberVoteId
                });
                if(request.IsApproved)
                {
                     memberVote.VoteResult = VoteResult.NotVotedYet;
                }
                // Update the member vote entity
                var updateResult = await _repository.ResolutionMemberVotes.UpdateAsync(memberVote);
                if (!updateResult)
                {
                    _logger.LogError(null, $"Failed to update ResolutionMemberVote with ID: {request.ResolutionMemberVoteId}");
                    return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorUpdatingData]);
                }

                _logger.LogInfo($"Successfully requested revote for ResolutionMemberVote ID: {request.ResolutionMemberVoteId} by user: {currentUserId}");

                // Return success message (MSG001)
                return Success<string>(_localizer[SharedResourcesKey.OperationCompletedSuccessfully]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in RequestRevote for ResolutionMemberVote ID: {request.ResolutionMemberVoteId}");
                return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorUpdatingData]);
            }
        }
        #endregion
    }
}
