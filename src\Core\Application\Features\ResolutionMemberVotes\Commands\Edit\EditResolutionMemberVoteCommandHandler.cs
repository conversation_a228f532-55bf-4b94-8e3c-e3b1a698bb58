﻿using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using AutoMapper;
using Domain.Entities.ResolutionManagement;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;
using Application.Features.ResolutionMemberVotes.Dtos;
using Domain.Services;
using System.Reflection;

namespace Application.Features.ResolutionMemberVotes.Commands.Edit
{
    public class EditResolutionMemberVoteCommandHandler : BaseResponse<PERSON>andler, ICommandHandler<EditResolutionMemberVoteCommand, BaseResponse<string>>
    {

        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _Repository;
        private readonly IMapper _mapper;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructors
        public EditResolutionMemberVoteCommandHandler(IRepositoryManager Repository, IMapper mapper, ILoggerManager logger, ICurrentUserService currentUserService)
        {
            _logger = logger;
            _Repository = Repository;
            _mapper = mapper;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Handle Functions
        public async Task<BaseResponse<string>> Handle(EditResolutionMemberVoteCommand request, CancellationToken cancellationToken)
        {
            try
            {
                if (request == null)
                    return BadRequest<string>("the request can't be blank");
                var member = await _Repository.BoardMembers.GetActiveBoardMemberByResolutionIdAsync(request.ResolutionId, _currentUserService.UserId.Value);
                var memberVote = await _Repository.ResolutionMemberVotes.GetByMemeberAndResolutionAsync(request.ResolutionId, member.Id, true); // Enable tracking for updates
                var resolution = await _Repository.Resolutions.GetByIdAsync<Resolution>(memberVote.ResolutionId, false);
                // Handle collection updates manually to avoid foreign key constraint issues                
                if (request.ItemsVote.Any())
                {
                    await UpdateResolutionItemVotes(memberVote, request.ItemsVote);
                    
                }    
                //if(request.VoteComments.Any())
                //{
                //    await UpdateResolutionVoteComments(memberVote, request.VoteComments);
                //}
                // Update other properties excluding collections
                request.CreatedBy = memberVote.CreatedBy; // Keep original creator  
                request.CreatedAt = memberVote.CreatedAt; // Keep original creation time
                
                memberVote.ResolutionMemberVoteStatusHistories.Add(new ResolutionMemberVoteStatusHistory
                {
                    StatusID = (int)ResolutionMemberVoteStatusEnum.Voted,
                });
                await AddResolutionItemVotes(memberVote, request.ItemsVote, (int)member.MemberType);
                await AddResolutionVoteComments(memberVote,request.VoteComments , (int)member.MemberType);
               
                _mapper.Map(request, memberVote);
                var memberVoteResult = new Domain.Entities.ResolutionManagement.Enums.VoteResult();
                if (memberVote.ResolutionItemVotes.Any() || memberVote.ResolutionItemVotes.Count > 0)
                    memberVote.VoteResult = VotingDomainService.CalculateResolutionMemberVoteResult(memberVote,
                        memberVote.ResolutionItemVotes!=null && memberVote.ResolutionItemVotes.Any() && memberVote.ResolutionItemVotes.Count != 0 
                        ?  memberVote.ResolutionItemVotes.Where(c=>c.VoteResult != Domain.Entities.ResolutionManagement.Enums.VoteResult.NotEligibleToVote).ToList() 
                        : null , resolution.MemberVotingResult);
                
                var status = await _Repository.ResolutionMemberVotes.UpdateAsync(memberVote);
                if (!status)
                    return BadRequest<string>("Update Operation Failed.");
                return Success("Update Operation Successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in EditResolutionMemberVoteCommand");
                return ServerError<string>(ex.Message);
            }
        }
         

        private async Task UpdateResolutionItemVotes(ResolutionMemberVote originalEntity, List<ResolutionItemVoteDto>? itemsVote)
        {          
            // Update existing and add new items
            foreach (var itemVoteDto in itemsVote)
            {
                var existingItem = originalEntity.ResolutionItemVotes.FirstOrDefault(x => x.Id == itemVoteDto.Id);
                
                //Edit ItemVote 
                if (existingItem != null)
                {
                    existingItem.VoteResult = itemVoteDto.VoteResult;
                    // Update existing item
                    itemVoteDto.CreatedBy = existingItem.CreatedBy; // Keep original creator
                    itemVoteDto.CreatedAt = existingItem.CreatedAt; // Keep original creation time
                }
                
            }
        }
        private async Task AddResolutionItemVotes(ResolutionMemberVote originalEntity, List<ResolutionItemVoteDto>? itemsVote, int memberType)
        {
            if (itemsVote != null && itemsVote.Any())
            {
                // Process item vote comments
                var itemVoteComments = new List<ResolutionItemVoteComment>();

                foreach (var itemVoteDto in itemsVote)
                {
                    var existingItem = originalEntity.ResolutionItemVotes.FirstOrDefault(x => x.Id == itemVoteDto.Id);

                    // Skip if existing item not found
                    if (existingItem == null)
                        continue;

                    // Process item comments if they exist
                    if (itemVoteDto.ItemComments != null && itemVoteDto.ItemComments.Any())
                    {
                        foreach (var itemVoteCommentDto in itemVoteDto.ItemComments.Where(c => c.Id == 0))
                        {
                            // Add new comment
                            var newComment = new ResolutionItemVoteComment
                            {
                                Comment = itemVoteCommentDto.Comment,
                                ResolutionItemID = existingItem.ResolutionItemId,
                                BoardMemberID = originalEntity.BoardMemberID,
                                UserRoleOrBoardMemberType = memberType.ToString(),
                                IsBoardMemberComment = true,
                            };
                            itemVoteComments.Add(newComment);
                        }
                    }
                }

                // Add all comments to database if any exist
                if (itemVoteComments.Any())
                {
                    await _Repository.ResolutionItemVoteComments.AddRangeAsync(itemVoteComments);
                }
            }
        }
        private async Task AddResolutionVoteComments(ResolutionMemberVote originalEntity, List<ResolutionVoteCommentDto> comments, int memberType)
        {
            if(comments != null && comments.Any())
            {
                var resolutionMemberVoteComments = new List<ResolutionMemberVoteComment>();
                // Add new comments (only those with Id == 0)
                foreach (var commentDTO in comments.Where(c => c.Id == 0))
                {
                    var comment = new ResolutionMemberVoteComment
                    {
                        Id = 0, // New comment
                        Comment = commentDTO.Comment,
                        ResolutionMemberVoteID = originalEntity.Id,
                        UserRoleOrBoardMemberType = memberType.ToString(),
                        IsBoardMemberComment = true // Board member is making the comment
                    };
                    resolutionMemberVoteComments.Add(comment);
                }

                if (resolutionMemberVoteComments.Any())
                {
                    await _Repository.ResolutionMemberVoteComments.AddRangeAsync(resolutionMemberVoteComments);
                }
            }
        }
        #endregion

    }
}
