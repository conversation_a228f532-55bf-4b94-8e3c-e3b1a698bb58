using Application.Base.Abstracts;
using Abstraction.Base.Response;

namespace Application.Features.ResolutionMemberVotes.Commands.RequestRevote
{
    /// <summary>
    /// Command for requesting a revote on a resolution member vote
    /// Follows established CQRS patterns in the Jadwa Fund Management System
    /// </summary>
    public record RequestRevoteCommand : ICommand<BaseResponse<string>>
    {
        /// <summary>
        /// The ID of the ResolutionMemberVote to request revote for
        /// </summary>
        public int ResolutionMemberVoteId { get; set; }
    }
}
