using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Domain.Entities.ResolutionManagement;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;
using Microsoft.Extensions.Localization;
using Resources;
using Domain.Entities.ResolutionManagement.Enums;
using Domain.Entities.FundManagement;
using Domain.Entities.Notifications;
using System;

namespace Application.Features.ResolutionMemberVotes.Commands.RequestRevote
{
    /// <summary>
    /// <PERSON><PERSON> for requesting a revote on a resolution member vote
    /// Follows Clean Architecture principles and established CQRS patterns
    /// Updates ResolutionMemberVote status and creates history entry
    /// </summary>
    public class RequestRevoteCommandHandler : BaseResponseHandler, ICommandHandler<RequestRevoteCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IStringLocalizer<SharedResources> _localizer;
        #endregion

        #region Constructors
        public RequestRevoteCommandHandler(
            IRepositoryManager repository,
            ILoggerManager logger,
            ICurrentUserService currentUserService,
            IStringLocalizer<SharedResources> localizer)
        {
            _logger = logger;
            _repository = repository;
            _currentUserService = currentUserService;
            _localizer = localizer;
        }
        #endregion

        #region Handle Functions
        public async Task<BaseResponse<string>> Handle(RequestRevoteCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Validate request
                if (request == null)
                    return BadRequest<string>(_localizer[SharedResourcesKey.EmptyRequestValidation]);

                if (request.ResolutionMemberVoteId <= 0)
                    return BadRequest<string>(_localizer[SharedResourcesKey.EmptyIdValidation]);

                // Get the ResolutionMemberVote entity with tracking enabled for updates
                var memberVote = await _repository.ResolutionMemberVotes.GetMemberVoteIncludeResolutionAsync(
                    request.ResolutionMemberVoteId, 
                    trackChanges: true);

                if (memberVote == null)
                    return NotFound<string>(_localizer["ResolutionMemberVoteNotFound"]);

                // Validate that the current user has permission to request revote
                // This could be enhanced with specific business rules if needed
                var currentUserId = _currentUserService.UserId;
                if (!currentUserId.HasValue)
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);

                // Create a new entry in ResolutionMemberVoteStatusHistory to track this status change
                // Following the established pattern from EditResolutionMemberVoteCommandHandler
                memberVote.ResolutionMemberVoteStatusHistories.Add(new ResolutionMemberVoteStatusHistory
                {
                    StatusID = (int) ResolutionMemberVoteStatusEnum.RequestRevote,
                    ResolutionMemberVoteID = request.ResolutionMemberVoteId
                });

                // Update the member vote entity
                var updateResult = await _repository.ResolutionMemberVotes.UpdateAsync(memberVote);
                if (!updateResult)
                {
                    _logger.LogError(null, $"Failed to update ResolutionMemberVote with ID: {request.ResolutionMemberVoteId}");
                    return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorUpdatingData]);
                }

                await AddNotification(memberVote.Resolution.FundId, memberVote);

                _logger.LogInfo($"Successfully requested revote for ResolutionMemberVote ID: {request.ResolutionMemberVoteId} by user: {currentUserId}");

                // Return success message (MSG001)
                return Success<string>(_localizer[SharedResourcesKey.OperationCompletedSuccessfully]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in RequestRevote for ResolutionMemberVote ID: {request.ResolutionMemberVoteId}");
                return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorUpdatingData]);
            }
        }
        #endregion


        private async Task AddNotification(int fundId, ResolutionMemberVote memberVote)
        {
            var notifications = new List<Domain.Entities.Notifications.Notification>();

            // Get fund details to access legal council and board secretaries
            var fundDetails = await _repository.Funds.ViewFundUsers(fundId, trackChanges: false);
            if (fundDetails == null) return;

            if (fundDetails.LegalCouncilId > 0)
            {
                notifications.Add(new Domain.Entities.Notifications.Notification
                {
                    Title = string.Empty,
                    Body = $"{memberVote?.Resolution?.Code}|{fundDetails.Name}",
                    FundId = fundDetails.Id,
                    UserId = fundDetails.LegalCouncilId,
                    NotificationType = (int)NotificationType.RequestRevote,
                });
            }

            var boardSecretaries = fundDetails.FundBoardSecretaries ?? new List<FundBoardSecretary>();
            foreach (var boardSecretary in boardSecretaries)
            {
                notifications.Add(new Domain.Entities.Notifications.Notification
                {
                    Title = string.Empty,
                    Body = $"{memberVote?.Resolution?.Code}|{fundDetails.Name}",
                    FundId = fundDetails.Id,
                    UserId = boardSecretary.UserId,
                    NotificationType = (int)NotificationType.RequestRevote, 
                });
            }

            if (notifications.Any())
            {
                await _repository.Notifications.AddRangeAsync(notifications);
                _logger.LogInfo($"Request ReVote notifications added Count: {notifications.Count}");
            }
        }
    }
}
