using Application.Base.Abstracts;
using Abstraction.Base.Response;

namespace Application.Features.ResolutionMemberVotes.Commands.ResendReminderNotification
{
    /// <summary>
    /// Command for resending reminder notification to board member about voting
    /// Follows established CQRS patterns in the Jadwa Fund Management System
    /// </summary>
    public record SendReminderNotificationCommand : ICommand<BaseResponse<string>>
    {
        /// <summary>
        /// The ID of the ResolutionMemberVote to resend reminder notification for
        /// </summary>
        public int ResolutionId { get; set; }
        public int BoardMemberId { get; set; }
    }
}
