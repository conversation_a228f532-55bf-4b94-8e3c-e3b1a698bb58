using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Domain.Entities.ResolutionManagement;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;
using Microsoft.Extensions.Localization;
using Resources;
using Domain.Entities.Notifications;
using Abstraction.Contracts.Identity;
using Abstraction.Constants;
using Domain.Entities.FundManagement;

namespace Application.Features.ResolutionMemberVotes.Commands.ResendReminderNotification
{
    /// <summary>
    /// Handler for resending reminder notification to board member about voting
    /// Follows Clean Architecture principles and established CQRS patterns
    /// Sends localized reminder notifications with role-based access control
    /// </summary>
    public class SendReminderNotificationCommandHandler : BaseResponseHandler, ICommandHandler<SendReminderNotificationCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly IIdentityServiceManager _identityService;
        #endregion

        #region Constructors
        public SendReminderNotificationCommandHandler(
            IRepositoryManager repository, 
            ILoggerManager logger, 
            ICurrentUserService currentUserService,
            IStringLocalizer<SharedResources> localizer,
            IIdentityServiceManager identityService)
        {
            _logger = logger;
            _repository = repository;
            _currentUserService = currentUserService;
            _localizer = localizer;
            _identityService = identityService;
        }
        #endregion

        #region Handle Functions
        public async Task<BaseResponse<string>> Handle(SendReminderNotificationCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Validate request
                if (request == null)
                    return BadRequest<string>(_localizer[SharedResourcesKey.EmptyRequestValidation]);

                if (request.ResolutionId <= 0)
                    return BadRequest<string>(_localizer[SharedResourcesKey.EmptyIdValidation]);

                // Get the member vote with full related data using the repository method
                var memberVotes = await _repository.ResolutionMemberVotes.GetMembersByResolutionIdAsync(
                    request.ResolutionId,
                    trackChanges: false);
                var memberVote = memberVotes.Where(c => c.BoardMemberID == request.BoardMemberId).FirstOrDefault();
                // Get the resolution and fund details           
                var fund = await _repository.Funds.GetByIdAsync<Fund>(memberVote.Resolution.FundId,trackChanges: false);

                if (fund == null)
                    return NotFound<string>(_localizer["FundNotFound"]);

                // Validate that the current user has permission to resend reminder notifications
                var currentUserId = _currentUserService.UserId;
                if (!currentUserId.HasValue)
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);
                var userRoles = _currentUserService.Roles;

                var hasPermission =  HasResendReminderPermission(fund, currentUserId.Value);
                if (!hasPermission)
                    return Unauthorized<string>(_localizer[SharedResourcesKey.UnauthorizedAccess]);

                // Get current user details for notification body                
                var currentUserName =_currentUserService.UserName;

                // Create reminder notification for the board member
                var notification = new Domain.Entities.Notifications.Notification
                {
                    UserId = memberVote.BoardMember.UserId,
                    FundId = fund.Id,
                    Title = string.Empty, // Will be localized by the service
                    Body = $"{memberVote.Resolution.Code}|{fund.Name}|''",
                    NotificationType = (int)NotificationType.SendVoteReminder, // Using existing voting notification type
                    IsRead = false
                };

                // Add the notification
                await _repository.Notifications.AddAsync(notification, cancellationToken);

                _logger.LogInfo($"Successfully resent reminder notification for ResolutionMemberVote ID: {request.ResolutionId} to user: {memberVote.BoardMember.UserId} by user: {currentUserId}");

                // Return success message (MSG001)
                return Success<string>(_localizer[SharedResourcesKey.OperationCompletedSuccessfully]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in ResendReminderNotification for ResolutionMemberVote ID: {request.ResolutionId}");
                return ServerError<string>(_localizer[SharedResourcesKey.SystemErrorUpdatingData]);
            }
        }
        #endregion

        #region Private Methods
 


        /// <summary>
        /// Validates if the current user has permission to resend reminder notifications
        /// Based on established role-based access patterns - only Legal Council/Board Secretary can resend reminders
        /// </summary>
        private  bool HasResendReminderPermission(Fund fundDetails, int currentUserId)
        {

            try
            {
                _logger.LogInfo($"Checking fund roles for User ID: {currentUserId} in Fund ID: {fundDetails.Id}");
                // Get fund details with all related entities

                if (fundDetails == null)
                {
                    _logger.LogWarn($"Fund not found with ID: {fundDetails.Id}");
                    return false;
                }

                // 1. Check if user is Legal Council for the fund
                else if (fundDetails.LegalCouncilId == currentUserId)
                {
                    
                    _logger.LogInfo($"User ID: {currentUserId} is Legal Council for Fund ID: {fundDetails.Id}");
                    return true;
                }

                // 2. Check if user is a Fund Manager for the fund
                else if (fundDetails.FundManagers != null && fundDetails.FundManagers.Count > 0)
                {
                    var isFundManager = fundDetails.FundManagers.Any(fm => fm.UserId == currentUserId);
                    if (isFundManager)
                    {
                        
                        _logger.LogInfo($"User ID: {currentUserId} is Fund Manager for Fund ID: {fundDetails.Id}");
                        return true;
                    }
                }

                // 3. Check if user is a Board Secretary for the fund
                else if (fundDetails.FundBoardSecretaries != null && fundDetails.FundBoardSecretaries.Count > 0)
                {
                    var isBoardSecretary = fundDetails.FundBoardSecretaries.Any(bs => bs.UserId == currentUserId);
                    if (isBoardSecretary)
                    {
                       
                        _logger.LogInfo($"User ID: {currentUserId} is Board Secretary for Fund ID: {fundDetails.Id}");
                        return true;
                    }
                }

                // 4. Check if user is a Board Member for the fund
                else if (fundDetails.BoardMembers != null && fundDetails.BoardMembers.Count > 0)
                {
                    var isBoardMember = fundDetails.BoardMembers.Any(bs => bs.UserId == currentUserId);
                    if (isBoardMember)
                    {
                        
                        _logger.LogInfo($"User ID: {currentUserId} is Board Member for Fund ID: {fundDetails.Id}");
                        return false;
                    }
                }
                return false;
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking fund roles for User ID: {currentUserId} in Fund ID: {fundDetails.Id}");
                return false;
            }
            
        }
        #endregion
    }
}
