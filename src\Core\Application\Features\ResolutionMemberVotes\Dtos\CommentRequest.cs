﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Abstraction.Base.Dto;

namespace Application.Features.ResolutionMemberVotes.Dtos
{
    public  record CommentRequest  
    { 
        public string? Comment { get; set; }
    }
    public record ItemCommentRequest : CommentRequest
    {
        public int ResolutionItemId { get; set; }
        public int MemberId { get; set; } // This is the ResolutionItemVoteId
    }
    public record MemberVoteCommentRequest : CommentRequest
    {
        public int ResolutionMemberVoteId { get; set; }
    }
}
