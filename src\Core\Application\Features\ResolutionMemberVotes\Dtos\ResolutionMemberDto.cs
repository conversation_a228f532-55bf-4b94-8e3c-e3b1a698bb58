﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Domain.Entities.FundManagement;
using Domain.Entities.ResolutionManagement.Enums;

namespace Application.Features.ResolutionMemberVotes.Dtos
{
    public record ResolutionMemberDto 
    {
        public string FullName { get; set; }
        public VoteResult VoteResult { get; set; }
        public string VoteResultDisplay { get; set; }
        public string ProfileImagePath { get; set; }
        public BoardMemberType BoardMemberType { get; set; }
        public string BoardMemberTypeDisplay { get; set; }
        public int BoardMemberId { get; set; }
        public int ResolutionId { get; set; }
        public bool HasRevoteRequest { get; set; }
        public bool ShowSendReminder { get; set; }
        
    }
}
