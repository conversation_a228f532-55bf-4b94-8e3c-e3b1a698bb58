using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Application.Features.ResolutionMemberVotes.Dtos;

namespace Application.Features.ResolutionMemberVotes.Queries.GetResolutionItemVoteComments
{
    /// <summary>
    /// Query to retrieve all comments associated with a specific ResolutionItemVote entity
    /// Returns a collection of comment DTOs with user role metadata and localized role display names
    /// Follows CQRS patterns established in the Jadwa Fund Management System
    /// </summary>
    public record GetResolutionItemVoteCommentsQuery : IQuery<BaseResponse<List<ResolutionItemVoteCommentDto>>>
    {
        /// <summary>
        /// The ID of the ResolutionItemVote to retrieve comments for
        /// </summary>
        public int Id { get; set; }
    }
}
