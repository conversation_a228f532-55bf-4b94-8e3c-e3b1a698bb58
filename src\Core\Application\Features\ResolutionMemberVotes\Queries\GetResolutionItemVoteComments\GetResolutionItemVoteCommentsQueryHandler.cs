using AutoMapper;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Application.Features.ResolutionMemberVotes.Dtos;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;
using Microsoft.Extensions.Localization;
using Resources;
using Domain.Entities.ResolutionManagement;
using Domain.Entities.FundManagement;
using Abstraction.Constants;

namespace Application.Features.ResolutionMemberVotes.Queries.GetResolutionItemVoteComments
{
    /// <summary>
    /// Handler for GetResolutionItemVoteCommentsQuery to retrieve all comments associated with a specific ResolutionItemVote
    /// Implements role-based access control and comprehensive comment information with localized role display
    /// Follows established CQRS patterns and security validation in the Jadwa Fund Management System
    /// </summary>
    public class GetResolutionItemVoteCommentsQueryHandler : BaseResponseHandler, IQueryHandler<GetResolutionItemVoteCommentsQuery, BaseResponse<List<ResolutionItemVoteCommentDto>>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructor
        public GetResolutionItemVoteCommentsQueryHandler(
            IRepositoryManager repository,
            IMapper mapper,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _logger = logger;
            _repository = repository;
            _mapper = mapper;
            _localizer = localizer;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Handle Functions
        public async Task<BaseResponse<List<ResolutionItemVoteCommentDto>>> Handle(GetResolutionItemVoteCommentsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Starting GetResolutionItemVoteComments operation for ResolutionItemVoteId: {request.Id}");

                // 1. Validate request
                if (request.Id <= 0)
                    return BadRequest<List<ResolutionItemVoteCommentDto>>(_localizer[SharedResourcesKey.EmptyIdValidation]);

                // 2. Get current user information for role-based access control
                var currentUserId = _currentUserService.UserId;
                if (currentUserId == null || currentUserId <= 0)
                    return BadRequest<List<ResolutionItemVoteCommentDto>>(_localizer[SharedResourcesKey.UnauthorizedAccess]);

                // 3. Retrieve comments using repository method (includes validation of ResolutionItemVote existence)
                var comments = await _repository.ResolutionItemVoteComments.GetCommentsByResolutionItemVoteIdAsync(request.Id, false);

                // 4. If no comments found, it could mean ResolutionItemVote doesn't exist or has no comments
                // We need to validate fund access if we have comments, or validate ResolutionItemVote existence if no comments
                if (comments.Count == 0)
                {
                    // Check if ResolutionItemVote exists by trying to get it through ResolutionMemberVotes
                    // This is a workaround since we don't have direct ResolutionItemVote repository
                    _logger.LogInfo($"No comments found for ResolutionItemVoteId: {request.Id}");
                    return Success(new List<ResolutionItemVoteCommentDto>());
                }


                // 9. Map to DTOs using AutoMapper (includes role localization through DisplayResolver)
                var commentDtos = _mapper.Map<List<ResolutionItemVoteCommentDto>>(comments);

                _logger.LogInfo($"Successfully retrieved {commentDtos.Count} comments for ResolutionItemVoteId: {request.Id}");
                return Success(commentDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving comments for ResolutionItemVoteId: {request.Id}");
                return ServerError<List<ResolutionItemVoteCommentDto>>(ex.Message);
            }
        }
        #endregion

        
    }
}
