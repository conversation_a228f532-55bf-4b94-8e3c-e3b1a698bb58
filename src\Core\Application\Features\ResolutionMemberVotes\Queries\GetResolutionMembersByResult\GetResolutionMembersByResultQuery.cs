﻿using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Application.Features.ResolutionMemberVotes.Dtos;
using Domain.Entities.ResolutionManagement.Enums;

namespace Application.Features.ResolutionMemberVotes.Queries.GetResolutionMembersByResult
{
    public record GetResolutionMembersByResultQuery : IQuery<BaseResponse<List<string>>>
    {
        public int? Id { get; set; }

        public ItemVoteResult ItemVoteResult { get; set; }
    }
}
