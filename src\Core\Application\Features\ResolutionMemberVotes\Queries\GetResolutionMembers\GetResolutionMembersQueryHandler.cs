﻿using AutoMapper;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Application.Features.ResolutionMemberVotes.Dtos;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;
using Abstraction.Constants;
using Domain.Entities.FundManagement;
using Domain.Entities.ResolutionManagement.Enums;


namespace Application.Features.ResolutionMemberVotes.Queries.Get
{
    public class GetResolutionMembersQueryHandler : BaseResponseHandler, IQueryHandler<GetResolutionMembersQuery, BaseResponse<ResolutionMembersDto>>
    {
        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _Repository;
        private readonly IMapper _mapper;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructor(s)
        public GetResolutionMembersQueryHandler(IRepositoryManager Repository, IMapper mapper, ILoggerManager logger , ICurrentUserService currentUserService)
        {
            _logger = logger;
            _Repository = Repository;
            _mapper = mapper;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<ResolutionMembersDto>> Handle(GetResolutionMembersQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var result =await  _Repository.ResolutionMemberVotes.GetMembersByResolutionIdAsync(request.Id.Value, false);
                var fundId = await _Repository.Resolutions.GetFundIdByResolutionIdAsync(request.Id.Value, false);
                var fund = await _Repository.Funds.ViewFundUsers(fundId, false);
                if (result == null)
                    return NotFound<ResolutionMembersDto>("ResolutionMemberVote with this Id not found!");
                var resolutionMembers = _mapper.Map<List<ResolutionMemberDto>>(result);
                var resultMapper = new ResolutionMembersDto();
                 resultMapper.Members = resolutionMembers;
                var userRole = await GetUserFundRole(fund, _currentUserService.UserId.Value);
                var userCanSendReminder = userRole == Roles.BoardSecretary || userRole == Roles.LegalCouncil;
                resultMapper.Members.ForEach(c => c.ShowSendReminder = (userCanSendReminder && c.VoteResult == VoteResult.NotVotedYet));
                foreach (var c in resultMapper.Members)
                {
                   c.HasRevoteRequest = await MemberHasReqestRevote( request.Id.Value , c.BoardMemberId);
                }
                return Success(resultMapper);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in GetResultByIdQuery");
                return ServerError<ResolutionMembersDto>(ex.Message);
            }
        }
        /// <summary>
        /// Determines the current user's role within a specific fund context
        /// Checks FundBoardSecretary table, Fund.LegalCouncilId field, and FundManager table
        /// Follows Clean Architecture and CQRS patterns with proper repository usage
        /// </summary>
        /// <param name="fundId">The fund ID to check roles for</param>
        /// <param name="currentUserId">The current user ID to check roles for</param>
        /// <returns>Comma-separated string of roles the user has in the fund, or empty string if no roles</returns>
        private async Task<Roles> GetUserFundRole(Fund fundDetails, int currentUserId)
        {
            try
            {
                _logger.LogInfo($"Checking fund roles for User ID: {currentUserId} in Fund ID: {fundDetails.Id}");

                var userRole = Roles.None;

                // 1. Check if user is Legal Council for the fund
                if (fundDetails.LegalCouncilId == currentUserId)
                {
                    userRole = Roles.LegalCouncil;
                    _logger.LogInfo($"User ID: {currentUserId} is Legal Council for Fund ID: {fundDetails.Id}");
                }

                // 2. Check if user is a Fund Manager for the fund
                if (fundDetails.FundManagers != null && fundDetails.FundManagers.Count > 0)
                {
                    var isFundManager = fundDetails.FundManagers.Any(fm => fm.UserId == currentUserId);
                    if (isFundManager)
                    {
                        userRole = Roles.FundManager;
                        _logger.LogInfo($"User ID: {currentUserId} is Fund Manager for Fund ID: {fundDetails.Id}");
                    }
                }

                // 3. Check if user is a Board Secretary for the fund
                if (fundDetails.FundBoardSecretaries != null && fundDetails.FundBoardSecretaries.Count > 0)
                {
                    var isBoardSecretary = fundDetails.FundBoardSecretaries.Any(bs => bs.UserId == currentUserId);
                    if (isBoardSecretary)
                    {
                        userRole = Roles.BoardSecretary;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Secretary for Fund ID: {fundDetails.Id}");
                    }
                }

                // 4. Check if user is a Board Member for the fund
                if (fundDetails.BoardMembers != null && fundDetails.BoardMembers.Count > 0)
                {
                    var isBoardMember = fundDetails.BoardMembers.Any(bs => bs.UserId == currentUserId);
                    if (isBoardMember)
                    {
                        userRole = Roles.BoardMember;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Member for Fund ID: {fundDetails.Id}");
                    }
                }

                // Return comma-separated roles or empty string if no roles found

                _logger.LogInfo($"User ID: {currentUserId} has roles in Fund ID: {fundDetails.Id}: '{userRole}'");

                return userRole;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking fund roles for User ID: {currentUserId} in Fund ID: {fundDetails.Id}");
                return Roles.None;
            }
        }

        private async Task<bool> MemberHasReqestRevote(int resolutionId , int memberId)
        {
           return await _Repository.ResolutionMemberVotes.ResolutionMemberVoteHasRevoteRequest(resolutionId,memberId, false);
        }
        #endregion
    }
}
