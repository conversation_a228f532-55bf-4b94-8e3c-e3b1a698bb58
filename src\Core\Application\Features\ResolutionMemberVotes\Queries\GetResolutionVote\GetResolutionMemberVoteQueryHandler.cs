﻿using AutoMapper;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Contract.Service;
using Application.Features.ResolutionMemberVotes.Dtos;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Microsoft.Extensions.Localization;
using Resources;
using Domain.Entities.FundManagement;
using Application.Common.Helpers;
using DocumentFormat.OpenXml.Bibliography;
using System.Security.Claims;
using Domain.Entities.Users;
using Abstraction.Constants;

namespace Application.Features.ResolutionMemberVotes.Queries.Get
{
    /// <summary>
    /// Handler for GetQuery to retrieve resolution details
    /// Implements role-based access control and comprehensive resolution information
    /// Based on Sprint.md requirements (JDWA-588, JDWA-584, JDWA-593, JDWA-589)
    /// </summary>
    public class GetResolutionMemberVoteQueryHandler : BaseResponseHandler, IQueryHandler<GetResolutionMemberVoteQuery, BaseResponse<ResolutionMemberVoteResponse>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        #endregion

        #region Constructor(s)
        public GetResolutionMemberVoteQueryHandler(
            IRepositoryManager repository,
            IMapper mapper,
            ILoggerManager logger,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService)
        {
            _logger = logger;
            _repository = repository;
            _mapper = mapper;
            _localizer = localizer;
            _currentUserService = currentUserService;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<ResolutionMemberVoteResponse>> Handle(GetResolutionMemberVoteQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInfo($"Starting GetResolution operation for ID: {request.Id}");

                // 1. Validate request
                if (request.Id <= 0)
                    return BadRequest<ResolutionMemberVoteResponse>(_localizer[SharedResourcesKey.InvalidIdValidation]);

                // 2. Get current user information for role-based access control
                var currentUserId = _currentUserService.UserId;
                if (request.MemeberId == 0 || !request.MemeberId.HasValue)
                    request.MemeberId = await _repository.Resolutions.GetBoardMemberIdByResolutionIdAndUserIdAsync(request.Id, _currentUserService.UserId.Value);
                // 3. Retrieve resolution with related data
                var resolution =  await _repository.Resolutions.GetResolutionVote(request.Id, request.MemeberId.Value, trackChanges: false);
                if (resolution == null)
                {
                    _logger.LogWarn($"Resolution not found with ID: {request.Id}");
                    return NotFound<ResolutionMemberVoteResponse>(_localizer[SharedResourcesKey.ResolutionNotFound]);
                }

                // 4. Build comprehensive resolution response
                var response =   _mapper.Map<ResolutionMemberVoteResponse>(resolution);
                var member = request.MemeberId != 0 ? await _repository.BoardMembers.GetBoardMemberIncludeUserByIdAsync(request.MemeberId.Value, false) : null;
                response.ResolutionMemberVote.BoardMemberName = member.User.FullName;
                response.ResolutionMemberVote.BoardMemberType = LocalizationHelper.GetBoardMemberTypeDisplay(member.MemberType, _localizer);
                response.ResolutionMemberVote.HasRevoteRequest = await _repository.ResolutionMemberVotes.ResolutionMemberVoteHasRevoteRequest(resolution.Id, member.Id, false);

                if (member.UserId!= _currentUserService.UserId)
                {
                    response.IsBoardMember = false;
                    response.UserFullName = _currentUserService.GetClaimValue(ClaimTypes.Name);
                    var userRole = await GetUserFundRole(resolution.FundId, _currentUserService.UserId.Value);
                    response.UserRoleOrBoardMemberType = LocalizationHelper.GetUserRoleDisplay(userRole.ToString(), _localizer);
                }
                else
                {
                    response.IsBoardMember = true;
                    response.UserFullName = member.User.FullName;
                    response.UserRoleOrBoardMemberType = LocalizationHelper.GetBoardMemberTypeDisplay(member.MemberType, _localizer);
                }
                _logger.LogInfo($"Resolution details retrieved successfully for ID: {request.Id}");
                return Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in GetResolution for ID: {request.Id}");
                return ServerError<ResolutionMemberVoteResponse>(_localizer[SharedResourcesKey.SystemErrorSavingData]);
            }
        }
        #endregion
        /// <summary>
        /// Determines the current user's role within a specific fund context
        /// Checks FundBoardSecretary table, Fund.LegalCouncilId field, and FundManager table
        /// Follows Clean Architecture and CQRS patterns with proper repository usage
        /// </summary>
        /// <param name="fundId">The fund ID to check roles for</param>
        /// <param name="currentUserId">The current user ID to check roles for</param>
        /// <returns>Comma-separated string of roles the user has in the fund, or empty string if no roles</returns>
        private async Task<Roles> GetUserFundRole(int fundId, int currentUserId)
        {
            try
            {
                _logger.LogInfo($"Checking fund roles for User ID: {currentUserId} in Fund ID: {fundId}");

                var userRole = Roles.None;
                // Get fund details with all related entities
                var fundDetails = await _repository.Funds.ViewFundUsers(fundId, trackChanges: false);
                if (fundDetails == null)
                {
                    _logger.LogWarn($"Fund not found with ID: {fundId}");
                    return Roles.None;
                }

                // 1. Check if user is Legal Council for the fund
                if (fundDetails.LegalCouncilId == currentUserId)
                {
                    userRole = Roles.LegalCouncil;
                    _logger.LogInfo($"User ID: {currentUserId} is Legal Council for Fund ID: {fundId}");
                }

                // 2. Check if user is a Fund Manager for the fund
                if (fundDetails.FundManagers != null && fundDetails.FundManagers.Count > 0)
                {
                    var isFundManager = fundDetails.FundManagers.Any(fm => fm.UserId == currentUserId);
                    if (isFundManager)
                    {
                        userRole = Roles.FundManager;
                        _logger.LogInfo($"User ID: {currentUserId} is Fund Manager for Fund ID: {fundId}");
                    }
                }

                // 3. Check if user is a Board Secretary for the fund
                if (fundDetails.FundBoardSecretaries != null && fundDetails.FundBoardSecretaries.Count > 0)
                {
                    var isBoardSecretary = fundDetails.FundBoardSecretaries.Any(bs => bs.UserId == currentUserId);
                    if (isBoardSecretary)
                    {
                        userRole = Roles.BoardSecretary;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Secretary for Fund ID: {fundId}");
                    }
                }
                if (fundDetails.BoardMembers != null && fundDetails.BoardMembers.Count > 0)
                {
                    var isBoardMember = fundDetails.BoardMembers.Any(bs => bs.UserId == currentUserId);
                    if (isBoardMember)
                    {
                        userRole = Roles.BoardMember;
                        _logger.LogInfo($"User ID: {currentUserId} is Board Secretary for Fund ID: {fundId}");
                    }
                }
                // Return comma-separated roles or empty string if no roles found

                _logger.LogInfo($"User ID: {currentUserId} has roles in Fund ID: {fundId}: '{userRole}'");

                return userRole;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking fund roles for User ID: {currentUserId} in Fund ID: {fundId}");
                return Roles.None;
            }
        }
    }
}
