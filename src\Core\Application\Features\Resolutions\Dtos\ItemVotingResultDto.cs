using Abstraction.Base.Dto;
using Domain.Entities.ResolutionManagement.Enums;
using Domain.Services;

namespace Application.Features.Resolutions.Dtos
{
    /// <summary>
    /// Data Transfer Object for ResolutionItem entity
    /// Contains properties for resolution items within a resolution
    /// Based on requirements in Sprint.md for resolution item management
    /// </summary>
    public record ItemVotingResultDto  
    {
        public int VotesCast { get; set; }
        public int ApproveVotes { get; set; }
        public int RejectVotes { get; set; }
        public int PendingVotes { get; set; }
        public VoteResult VoteResult { get; set; }
        public string VoteResultDisplay { get; set; }
    }
}
