using Application.Common.Helpers;
using Application.Features.AssessmentManagement.Dtos;
using Application.Features.Resolutions.Dtos;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Entities.ResolutionManagement;
using System.Linq;

namespace Application.Mapping.AssessmentManagement
{
    /// <summary>
    /// Mapping configurations for retrieving Assessment entities
    /// Maps from domain entities to DTOs for read operations
    /// Follows the same pattern as Resolution GetResolutionMapping for consistency
    /// </summary>
    public partial class AssessmentProfile
    {
        public void GetAssessmentMapping()
        {
            // Assessment entity to AssessmentDto
            CreateMap<Assessment, AssessmentDto>()
                .ForMember(dest => dest.FundName, opt => opt.MapFrom(src => src.Fund != null ? src.Fund.Name : string.Empty))
                .ForMember(dest => dest.StatusDisplayName, opt => opt.Ignore()) // Will be set by localization
                .ForMember(dest => dest.CreatedByName, opt => opt.Ignore()) // Will be resolved separately
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => src.UpdatedAt));

            // Assessment entity to AssessmentListDto
            CreateMap<Assessment, AssessmentListDto>()
                .ForMember(dest => dest.FundName, opt => opt.MapFrom(src => src.Fund != null ? src.Fund.Name : string.Empty))
                .ForMember(dest => dest.TypeDisplayName, opt => opt.Ignore()) // Will be set by localization
                .ForMember(dest => dest.StatusDisplayName, opt => opt.Ignore()) // Will be set by localization
                .ForMember(dest => dest.QuestionCount, opt => opt.MapFrom(src => src.Questions != null ? src.Questions.Count : 0))
                .ForMember(dest => dest.AttachmentCount, opt => opt.MapFrom(src => src.Attachment != null ? 1 : 0))
                .ForMember(dest => dest.TotalExpectedResponses, opt => opt.MapFrom(src => src.Responses != null ? src.Responses.Count : 0))
                .ForMember(dest => dest.CompletedResponses, opt => opt.MapFrom(src => src.Responses != null ? src.Responses.Count(r => r.Status == Domain.Entities.AssessmentManagement.Enums.ResponseStatus.Completed) : 0))
                .ForMember(dest => dest.PendingResponses, opt => opt.MapFrom(src => src.Responses != null ? src.Responses.Count(r => r.Status == Domain.Entities.AssessmentManagement.Enums.ResponseStatus.Pending) : 0))
                .ForMember(dest => dest.CompletionPercentage, opt => opt.MapFrom(src => CalculateCompletionPercentage(src)))
                .ForMember(dest => dest.CreatedByName, opt => opt.Ignore()) // Will be resolved separately


                .ForMember(dest => dest.Priority, opt => opt.MapFrom(src => CalculatePriority(src)))
                // Action permissions will be set in the handler based on user role and status
                .ForMember(dest => dest.CanEdit, opt => opt.Ignore())
                .ForMember(dest => dest.CanDelete, opt => opt.Ignore())
                .ForMember(dest => dest.CanApprove, opt => opt.Ignore())
                .ForMember(dest => dest.CanReject, opt => opt.Ignore())
                .ForMember(dest => dest.CanDistribute, opt => opt.Ignore())
                .ForMember(dest => dest.CanViewResults, opt => opt.Ignore())
                .ForMember(dest => dest.CanRespond, opt => opt.Ignore());

            // Assessment entity to SingleAssessmentResponse
            CreateMap<Assessment, SingleAssessmentResponse>()
                .ForMember(dest => dest.FundName, opt => opt.MapFrom(src => src.Fund != null ? src.Fund.Name : string.Empty))
                .ForMember(dest => dest.AssessmentType, opt => opt.MapFrom(src => MapAssessmentType(src.Type)))
                .ForMember(dest => dest.AssessmentStatus, opt => opt.MapFrom(src => MapAssessmentStatus(src.Status)))
                .ForMember(dest => dest.LastUpdated, opt => opt.MapFrom(src => src.UpdatedAt ?? src.CreatedAt))
                .ForMember(dest => dest.StatusId, opt => opt.MapFrom(src => (int)src.Status))
                .ForMember(dest => dest.CreatorID, opt => opt.MapFrom(src => src.CreatedBy))
                .ForMember(dest => dest.QuestionCount, opt => opt.MapFrom(src => src.Questions != null ? src.Questions.Count : 0))
                .ForMember(dest => dest.AttachmentCount, opt => opt.MapFrom(src => src.Attachment != null ? 1 : 0))
                .ForMember(dest => dest.TotalExpectedResponses, opt => opt.MapFrom(src => src.Responses != null ? src.Responses.Count : 0))
                .ForMember(dest => dest.CompletedResponses, opt => opt.MapFrom(src => src.Responses != null ? src.Responses.Count(r => r.Status == Domain.Entities.AssessmentManagement.Enums.ResponseStatus.Completed) : 0))
                .ForMember(dest => dest.CompletionPercentage, opt => opt.MapFrom(src => CalculateCompletionPercentage(src)))
                .ForMember(dest => dest.CreatedByName, opt => opt.MapFrom(src => src.CreatedByUser != null ? src.CreatedByUser.FullName : string.Empty))
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
                //.ForMember(dest => dest.CreatedByName, opt => opt.Ignore()) // Will be resolved separately
                // Action permissions will be set in the handler based on user role and status
                .ForMember(dest => dest.CanEdit, opt => opt.Ignore())
                .ForMember(dest => dest.CanDelete, opt => opt.Ignore());

            // Assessment entity to AssessmentDetailsDto
            CreateMap<Assessment, AssessmentDetailsDto>()
                .IncludeBase<Assessment, AssessmentDto>()
                .ForMember(dest => dest.Responses, opt => opt.MapFrom(src => src.Responses))
                .ForMember(dest => dest.Questions, opt => opt.MapFrom(src => src.Type == AssessmentType.Questionnaire ? src.Questions.OrderBy(q => q.DisplayOrder) : null))
                .ForMember(dest => dest.Attachment, opt => opt.MapFrom(src => src.Type == AssessmentType.Attachment ? src.Attachment : null))
                .ForMember(dest => dest.StatusHistory, opt => opt.MapFrom(src => src.StatusHistories.OrderByDescending(sh => sh.CreatedAt)))
                .ForMember(dest => dest.Statistics, opt => opt.Ignore()) // Will be calculated in handler
                .ForMember(dest => dest.AssignedBoardMembers, opt => opt.Ignore()) // Will be calculated in handler
                .ForMember(dest => dest.AllowedTransitions, opt => opt.Ignore()) // Will be set in handler
                .ForMember(dest => dest.ValidationMessages, opt => opt.Ignore()) // Will be set in handler
                .ForMember(dest => dest.CanEdit, opt => opt.Ignore()) // Will be set in handler
                .ForMember(dest => dest.CanDelete, opt => opt.Ignore()) // Will be set in handler
                .ForMember(dest => dest.CanApprove, opt => opt.Ignore()) // Will be set in handler
                .ForMember(dest => dest.CanReject, opt => opt.Ignore()) // Will be set in handler
                .ForMember(dest => dest.CanDistribute, opt => opt.Ignore()) // Will be set in handler
                .ForMember(dest => dest.RejectionReason, opt => opt.Ignore()) // Will be set in handler
                .ForMember(dest => dest.DistributionDate, opt => opt.Ignore()) // Will be set in handler
                .ForMember(dest => dest.CompletionDate, opt => opt.Ignore()) // Will be set in handler
                .ForMember(dest => dest.LastResponseDate, opt => opt.Ignore()) // Will be set in handler
                .ForMember(dest => dest.AverageResponseTime, opt => opt.Ignore()); // Will be set in handler

            // Assessment entity to AssessmentResultsDto
            CreateMap<Assessment, AssessmentResultsDto>()
                .IncludeBase<Assessment, AssessmentDto>()
                .ForMember(dest => dest.Questions, opt => opt.MapFrom(src => src.Type == AssessmentType.Questionnaire ? src.Questions.OrderBy(q => q.DisplayOrder) : null))
                .ForMember(dest => dest.Attachment, opt => opt.MapFrom(src => src.Type == AssessmentType.Attachment ? src.Attachment : null))
                .ForMember(dest => dest.StatusHistory, opt => opt.MapFrom(src => src.StatusHistories.OrderByDescending(sh => sh.CreatedAt)))
                .ForMember(dest => dest.Response, opt => opt.Ignore()) // Will be set in handler
                .ForMember(dest => dest.CanResponse, opt => opt.Ignore()); // Will be set in handler

            CreateMap<AssessmentQuestion, CreateAssessmentQuestionDto>()
                .ForMember(dest => dest.Order, opt => opt.MapFrom(src => src.DisplayOrder))
                .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.QuestionType));

            CreateMap<Option, CreateAssessmentOptionDto>()
                .ForMember(dest => dest.OptionText, opt => opt.MapFrom(src => src.Value));

            CreateMap<AssessmentResponse, AssessmentResponseDto>()
                .ForMember(dest => dest.MemberName, opt => opt.MapFrom(src => src.BoardMember != null && src.BoardMember.User != null ? src.BoardMember.User.FullName : string.Empty))
                .ForMember(dest => dest.MemberType, opt => opt.MapFrom(src => src.BoardMember != null ? src.BoardMember.MemberType : 0))
                .ForMember(dest => dest.MemberId, opt => opt.MapFrom(src => src.BoardMember != null ? src.BoardMember.Id : 0))
                .ForMember(dest => dest.StatusDisplayName, opt => opt.MapFrom(src => src.Status.ToString()))
                .ForMember(dest => dest.AssessmentTitle, opt => opt.MapFrom(src => src.Assessment != null ? src.Assessment.Title : string.Empty))
                .ForMember(dest => dest.FundId, opt => opt.MapFrom(src => src.Assessment != null ? src.Assessment.FundId : 0))
                .ForMember(dest => dest.Answers, opt => opt.Ignore()) // Will be mapped separately if needed
                .ForMember(dest => dest.Attachments, opt => opt.Ignore()) // Will be mapped separately if needed
                .ForMember(dest => dest.CompletionPercentage, opt => opt.Ignore()) // Will be calculated separately
                .ForMember(dest => dest.AnsweredQuestions, opt => opt.Ignore()); // Will be calculated separately


            // Assessment entity to AssessmentByIdDto (for GET /api/assessments/{id} endpoint)
            CreateMap<Assessment, AssessmentByIdDto>()
                .ForMember(dest => dest.FundName, opt => opt.MapFrom(src => src.Fund != null ? src.Fund.Name : string.Empty))
                .ForMember(dest => dest.Questions, opt => opt.MapFrom(src => src.Type == Domain.Entities.AssessmentManagement.Enums.AssessmentType.Questionnaire ? src.Questions.OrderBy(q => q.DisplayOrder) : null))
                .ForMember(dest => dest.Attachment, opt => opt.MapFrom(src => src.Type == Domain.Entities.AssessmentManagement.Enums.AssessmentType.Attachment ? src.Attachment : null))
                .ForMember(dest => dest.StatusHistory, opt => opt.MapFrom(src => src.StatusHistories.OrderByDescending(sh => sh.CreatedAt)))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                // Permission properties are set manually in the handler based on user role and assessment status
                .ForMember(dest => dest.CanEdit, opt => opt.Ignore())
                .ForMember(dest => dest.CanDelete, opt => opt.Ignore())
                .ForMember(dest => dest.CanApprove, opt => opt.Ignore())
                .ForMember(dest => dest.CanReject, opt => opt.Ignore())
                .ForMember(dest => dest.CanDistribute, opt => opt.Ignore())
                .ForMember(dest => dest.CanAnswer, opt => opt.Ignore());

            // AssessmentQuestion entity to AssessmentQuestionByIdDto
            CreateMap<AssessmentQuestion, AssessmentQuestionByIdDto>()
                .ForMember(dest => dest.Options, opt => opt.MapFrom(src => src.Options.OrderBy(o => o.Order)));

            // Option entity to AssessmentOptionByIdDto
            CreateMap<Option, AssessmentOptionByIdDto>();

            // Attachment entity to AssessmentAttachmentByIdDto
            CreateMap<Domain.Entities.Shared.Attachment, AssessmentAttachmentByIdDto>();

            // Attachment entity to CreateAssessmentAttachmentDto (for AssessmentDetailsDto)
            CreateMap<Domain.Entities.Shared.Attachment, CreateAssessmentAttachmentDto>()
                .ForMember(dest => dest.AttachmentId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Description, opt => opt.Ignore()) // Not available in Attachment entity
                .ForMember(dest => dest.Order, opt => opt.Ignore()); // Not available in Attachment entity

            // AssessmentStatusHistory entity to AssessmentStatusHistoryDto
            CreateMap<AssessmentStatusHistory, AssessmentStatusHistoryDto>()
                 .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.NewStatus))
                 .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.CreatedByUser.FullName));

            // AssessmentStatusHistory entity to AssessmentStatusHistoryByIdDto
            CreateMap<AssessmentStatusHistory, AssessmentStatusHistoryByIdDto>()
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => (AssessmentStatus)src.AssessmentStatusId))
                .ForMember(dest => dest.StatusDisplayName, opt => opt.Ignore()) // Will be set by localization service
                .ForMember(dest => dest.ActionDisplayName, opt => opt.Ignore()) // Will be set by localization service
                .ForMember(dest => dest.CreatedByName, opt => opt.MapFrom(src => src.CreatedByUser != null ? src.CreatedByUser.UserName : string.Empty));

          
        }

        #region Helper Methods

        /// <summary>
        /// Maps AssessmentType enum to AssessmentTypeDto
        /// </summary>
        private static AssessmentTypeDto MapAssessmentType(AssessmentType type)
        {
            return new AssessmentTypeDto
            {
                Id = (int)type,
                Value = type,
                Description = type.ToString(),
                NameAr = GetAssessmentTypeNameAr(type),
                NameEn = GetAssessmentTypeNameEn(type)
            };
        }

        /// <summary>
        /// Maps AssessmentStatus enum to AssessmentStatusDto
        /// </summary>
        private static AssessmentStatusDto MapAssessmentStatus(AssessmentStatus status)
        {
            return new AssessmentStatusDto
            {
                Id = (int)status,
                Value = status,
                Description = status.ToString(),
                NameAr = GetAssessmentStatusNameAr(status),
                NameEn = GetAssessmentStatusNameEn(status)
            };
        }

        /// <summary>
        /// Calculates completion percentage for an assessment
        /// </summary>
        private static decimal CalculateCompletionPercentage(Assessment assessment)
        {
            if (assessment.Responses == null || !assessment.Responses.Any())
                return 0;

            var totalResponses = assessment.Responses.Count;
            var completedResponses = assessment.Responses.Count(r => r.Status == Domain.Entities.AssessmentManagement.Enums.ResponseStatus.Completed);

            return totalResponses > 0 ? Math.Round((decimal)completedResponses / totalResponses * 100, 2) : 0;
        }



        /// <summary>
        /// Calculates priority based on status
        /// </summary>
        private static string CalculatePriority(Assessment assessment)
        {
            // Priority based on assessment status
            return assessment.Status switch
            {
                Domain.Entities.AssessmentManagement.Enums.AssessmentStatus.WaitingForApproval => "High",
                Domain.Entities.AssessmentManagement.Enums.AssessmentStatus.Active => "High",
                Domain.Entities.AssessmentManagement.Enums.AssessmentStatus.Rejected => "Medium",
                Domain.Entities.AssessmentManagement.Enums.AssessmentStatus.Draft => "Low",
                _ => "Medium"
            };
        }

        /// <summary>
        /// Gets Arabic name for assessment type
        /// </summary>
        private static string GetAssessmentTypeNameAr(AssessmentType type)
        {
            return type switch
            {
                AssessmentType.Questionnaire => "استبيان",
                AssessmentType.Attachment => "مرفق",
                _ => type.ToString()
            };
        }

        /// <summary>
        /// Gets English name for assessment type
        /// </summary>
        private static string GetAssessmentTypeNameEn(AssessmentType type)
        {
            return type switch
            {
                AssessmentType.Questionnaire => "Questionnaire",
                AssessmentType.Attachment => "Attachment",
                _ => type.ToString()
            };
        }

        /// <summary>
        /// Gets Arabic name for assessment status
        /// </summary>
        private static string GetAssessmentStatusNameAr(AssessmentStatus status)
        {
            return status switch
            {
                AssessmentStatus.Draft => "مسودة",
                AssessmentStatus.WaitingForApproval => "في انتظار الموافقة",
                AssessmentStatus.Approved => "موافق عليه",
                AssessmentStatus.Rejected => "مرفوض",
                AssessmentStatus.Active => "نشط",
                AssessmentStatus.Completed => "مكتمل",
                _ => status.ToString()
            };
        }

        /// <summary>
        /// Gets English name for assessment status
        /// </summary>
        private static string GetAssessmentStatusNameEn(AssessmentStatus status)
        {
            return status switch
            {
                AssessmentStatus.Draft => "Draft",
                AssessmentStatus.WaitingForApproval => "Waiting for Approval",
                AssessmentStatus.Approved => "Approved",
                AssessmentStatus.Rejected => "Rejected",
                AssessmentStatus.Active => "Active",
                AssessmentStatus.Completed => "Completed",
                _ => status.ToString()
            };
        }

        #endregion
    }
}
