using Application.Features.AssessmentManagement.Commands.UpdateAssessment;
using Application.Features.AssessmentManagement.Dtos;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;

namespace Application.Mapping.AssessmentManagement
{
    /// <summary>
    /// Mapping configurations for updating Assessment entities
    /// Maps from DTOs to domain entities for update operations
    /// Follows the same pattern as Resolution EditResolutionMapping for consistency
    /// </summary>
    public partial class AssessmentProfile
    {
        public void UpdateAssessmentMapping()
        {
            // UpdateAssessmentCommand to Assessment entity
            CreateMap<UpdateAssessmentCommand, Assessment>()
                .ForMember(dest => dest.Id, opt => opt.Ignore()) // ID should not be updated
                .ForMember(dest => dest.Status, opt => opt.Ignore()) // Status handled by state pattern
                .ForMember(dest => dest.Fund, opt => opt.Ignore())
                .ForMember(dest => dest.Attachment, opt => opt.Ignore())
                .ForMember(dest => dest.Reviewer, opt => opt.Ignore())
                .ForMember(dest => dest.Responses, opt => opt.Ignore())
                .ForMember(dest => dest.StatusHistories, opt => opt.Ignore())
                .ForMember(dest => dest.ReviewerComments, opt => opt.Ignore())
                .ForMember(dest => dest.ReviewedBy, opt => opt.Ignore())
                .ForMember(dest => dest.ReviewedDate, opt => opt.Ignore())
                // Audit properties handled by audit system
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
                .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
                // State pattern properties
                .ForMember(dest => dest.StateContext, opt => opt.Ignore())

                .ForMember(dest => dest.Questions, opt => opt.Ignore()); // Handled separately in handler

            // UpdateAssessmentDto to Assessment entity (for direct DTO mapping if needed)
            CreateMap<UpdateAssessmentDto, Assessment>()
                .ForMember(dest => dest.Id, opt => opt.Ignore()) // ID should not be updated
                .ForMember(dest => dest.Status, opt => opt.Ignore()) // Status handled by state pattern
                .ForMember(dest => dest.Fund, opt => opt.Ignore())
                .ForMember(dest => dest.Attachment, opt => opt.Ignore())
                .ForMember(dest => dest.Reviewer, opt => opt.Ignore())
                .ForMember(dest => dest.Responses, opt => opt.Ignore())
                .ForMember(dest => dest.StatusHistories, opt => opt.Ignore())
                .ForMember(dest => dest.ReviewerComments, opt => opt.Ignore())
                .ForMember(dest => dest.ReviewedBy, opt => opt.Ignore())
                .ForMember(dest => dest.ReviewedDate, opt => opt.Ignore())
                .ForMember(dest => dest.Questions, opt => opt.Ignore()) // Handled separately
                // Audit properties handled by audit system
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
                .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
                // State pattern properties
                .ForMember(dest => dest.StateContext, opt => opt.Ignore())
;

            // CreateAssessmentQuestionDto to AssessmentQuestion entity (for update operations)
            // Reuses the same DTO as create operations following established patterns
            CreateMap<CreateAssessmentQuestionDto, AssessmentQuestion>()
                .ForMember(dest => dest.Answers, opt => opt.Ignore())
                .ForMember(dest => dest.AssessmentId, opt => opt.MapFrom(src => src.AssessmentId))
                .ForMember(dest => dest.QuestionText, opt => opt.MapFrom(src => src.QuestionText))
                .ForMember(dest => dest.QuestionType, opt => opt.MapFrom(src => src.Type))
                .ForMember(dest => dest.DisplayOrder, opt => opt.MapFrom(src => src.Order))
                .ForMember(dest => dest.IsRequired, opt => opt.MapFrom(src => src.IsRequired))
                // Audit properties handled by audit system
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
                .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedByUser, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedByUser, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedByUser, opt => opt.Ignore());

            // CreateAssessmentOptionDto to Option entity (for update operations)
            // Reuses the same DTO as create operations following established patterns
            CreateMap<CreateAssessmentOptionDto, Option>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.QuestionId, opt => opt.Ignore()) // Will be set by relationship
                .ForMember(dest => dest.Question, opt => opt.Ignore())
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.OptionText))
                .ForMember(dest => dest.Order, opt => opt.MapFrom(src => src.Order))
                // Audit properties handled by audit system
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
                .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedByUser, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedByUser, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedByUser, opt => opt.Ignore());
        }
    }
}
