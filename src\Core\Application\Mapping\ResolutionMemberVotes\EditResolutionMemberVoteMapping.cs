﻿using Application.Features.ResolutionMemberVotes.Commands.Edit;
using Application.Features.ResolutionMemberVotes.Dtos;
using Application.Mapping.Resolutions;
using Domain.Entities.ResolutionManagement;

namespace Application.Mapping
{
    public partial class ResolutionMemberVotesProfile
    {
        public void EditResolutionMemberVoteMapping()
        {


            // Note: This mapping is only used in EditResolutionMemberVoteCommandHandler
            // It should not interfere with query operations
            CreateMap<ResolutionMemberVoteEditDto, ResolutionMemberVote>()
             .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
             .ForMember(dest => dest.ResolutionId, opt => opt.MapFrom(src => src.ResolutionId))
             .ForMember(dest => dest.VoteResult, opt => opt.MapFrom(src => src.VoteResult))
             .ForMember(dest => dest.ResolutionItemVotes, opt => opt.Ignore()) // Handled manually in handler
             .ForMember(dest => dest.ResolutionMemberVoteComments, opt => opt.Ignore()) // Handled manually in handler
             .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
             .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
             .ForMember(dest => dest.BoardMemberID, opt => opt.Ignore()) // Set manually in handler
             .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
             .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore());

            // Map ResolutionItemVoteDto to ResolutionItemVote for edit operations
            CreateMap<ResolutionItemVoteDto, ResolutionItemVote>()
             .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
             .ForMember(dest => dest.ResolutionItemId, opt => opt.MapFrom(src => src.ResolutionItemId))
             .ForMember(dest => dest.VoteResult, opt => opt.MapFrom(src => src.VoteResult))
             .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
             .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt));

            // IMPORTANT: These DTO-to-Entity mappings are commented out to prevent AutoMapper
            // from using them during query operations, which was causing database constraint violations.
            // The EditResolutionMemberVoteCommandHandler handles comment creation manually.

            /*
            // Map ResolutionVoteCommentDto to ResolutionMemberVoteComment for edit operations
            // Note: This mapping should only be used in edit scenarios where UserRoleOrBoardMemberType is set manually
            CreateMap<ResolutionVoteCommentDto, ResolutionMemberVoteComment>()
             .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
             .ForMember(dest => dest.Comment, opt => opt.MapFrom(src => src.Comment))
             .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
             .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
             .ForMember(dest => dest.UserRoleOrBoardMemberType, opt => opt.MapFrom(src => src.UserRoleOrBoardMemberType))
             .ForMember(dest => dest.IsBoardMemberComment, opt => opt.Ignore()) // Set manually in handler
             .ForMember(dest => dest.ResolutionMemberVoteID, opt => opt.Ignore()) // Set manually in handler
             .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
             .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore());

            CreateMap<ResolutionItemVoteCommentDto, ResolutionItemVoteComment>()
              .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
              .ForMember(dest => dest.Comment, opt => opt.MapFrom(src => src.Comment))
              .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
              .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
              .ForMember(dest => dest.UserRoleOrBoardMemberType, opt => opt.MapFrom(src => src.UserRoleOrBoardMemberType))
              .ForMember(dest => dest.IsBoardMemberComment, opt => opt.Ignore()) // Set manually in handler
              .ForMember(dest => dest.ResolutionItemID, opt => opt.Ignore()) // Set manually in handler
              .ForMember(dest => dest.BoardMemberID, opt => opt.Ignore()) // Set manually in handler
              .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
              .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore());
            */
            //CreateMap<ResolutionMemberVote, ResolutionMemberVoteDto>()
            // .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            // .ForMember(dest => dest.ResolutionId, opt => opt.MapFrom(src => src.ResolutionId))
            // .ForMember(dest => dest.ItemsVote, opt => opt.MapFrom(src => src.ResolutionItemVotes))
            // .ForMember(dest => dest.VoteComments, opt => opt.MapFrom(src => src.ResolutionMemberVoteComments))
            // .ForMember(dest => dest.VoteResult, opt => opt.MapFrom(src => src.VoteResult))
            // .ForMember(dest => dest.VoteResultDisplay, opt => opt.MapFrom<MemberVotingResultDisplayResolver>())
            // .ForMember(dest => dest.BoardMemberName, opt => opt.MapFrom(src => src.BoardMember.User.FullName))
            // .ForMember(dest => dest.BoardMemberType, opt => opt.MapFrom<BoardMemberTypeDisplayResolver>())
            // .ForMember(dest => dest.HasRevoteRequest, opt => opt.Ignore())
            // .ForMember(dest => dest.BoardMemberType, opt => opt.Ignore())
            // .ForMember(dest => dest.BoardMemberName, opt => opt.Ignore())
            // .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
            // .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
            // .ReverseMap();

            //CreateMap<ResolutionItemVote, ResolutionItemVoteDto>()
            //    .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            //    .ForMember(dest => dest.ResolutionItemId, opt => opt.MapFrom(src => src.ResolutionItemId))
            //    .ForMember(dest => dest.VoteResult, opt => opt.MapFrom(src => src.VoteResult))
            //    .ForMember(dest => dest.VoteResultDisplay, opt => opt.MapFrom<MemberItemVotingResultDisplayResolver>())
            //    .ForMember(dest => dest.ItemComments, opt => opt.MapFrom(src => src.ResolutionItem.ResolutionItemVoteComments.Where(c => c.BoardMemberID == src.ResolutionMemberVote.BoardMemberID)))
            //    .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
            //    .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
            //    .ReverseMap();



            //CreateMap<ResolutionItemVoteComment, ResolutionItemVoteCommentDto>()
            //    .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            //    .ForMember(dest => dest.Comment, opt => opt.MapFrom(src => src.Comment))
            //    .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
            //    .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
            //    .ForMember(dest => dest.UserRoleOrBoardMemberType, opt => opt.MapFrom<DisplayResolverForItemVoteCommentUserRole>())
            //    .ReverseMap();



        }
    }
}
