﻿using Application.Features.ResolutionMemberVotes.Dtos;
using Domain.Entities.ResolutionManagement;

namespace Application.Mapping
{
    public partial class ResolutionMemberVotesProfile
    {
        public void GetResolutionMemberVoteMapping()
        {
            // This mapping is handled in ResolutionVoteMapping.cs
            // The Resolution -> ResolutionMemberVoteResponse mapping includes:
            // - ResolutionMemberVote property mapping from src.ResolutionMemberVotes.FirstOrDefault()
            // - Additional properties are set manually in the handler after mapping
        }
    }
}
