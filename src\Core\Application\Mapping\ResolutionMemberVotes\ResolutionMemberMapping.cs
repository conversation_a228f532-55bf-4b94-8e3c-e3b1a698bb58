﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Features.ResolutionMemberVotes.Dtos;
using Application.Mapping.Resolutions;
using Domain.Entities.ResolutionManagement;
using Domain.Services;

namespace Application.Mapping
{
    public partial class ResolutionMemberVotesProfile
    {
        public void ResolutionMemberMapping()
        {


            CreateMap<ResolutionMemberVote, ResolutionMemberDto>()
                .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.BoardMember.User.FullName))
                .ForMember(dest => dest.ProfileImagePath, opt => opt.MapFrom(src => ""))
                .ForMember(dest => dest.BoardMemberType, opt => opt.MapFrom(src => src.BoardMember.MemberType))
                .ForMember(dest => dest.BoardMemberTypeDisplay, opt => opt.MapFrom<BoardMemberTypeDisplayResolver>())
                .ForMember(dest => dest.BoardMemberId, opt => opt.MapFrom(src => src.BoardMemberID))
                .ForMember(dest => dest.ResolutionId, opt => opt.MapFrom(src => src.ResolutionId))
                .ForMember(dest => dest.VoteResult, opt => opt.MapFrom(src => src.VoteResult))
                .ForMember(dest => dest.VoteResultDisplay, opt => opt.MapFrom<MemberVotingResultDisplayResolver>());
        }
    }
}
