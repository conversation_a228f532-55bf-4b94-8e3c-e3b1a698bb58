using AutoMapper;
using Application.Common.Helpers;
using Domain.Entities.ResolutionManagement;
using Domain.Entities.FundManagement;
using Microsoft.Extensions.Localization;
using Resources;
using Domain.Services;
using Abstraction.Constants;
using Abstraction.Contracts.Repository;
using Domain.Entities.ResolutionManagement.Enums;

namespace Application.Mapping.Resolutions
{
    /// <summary>
    /// Custom AutoMapper value resolver for localized resolution status display
    /// Provides localized text for resolution status using SharedResources
    /// </summary>
    public class ResolutionStatusDisplayResolver : IValueResolver<Resolution, object, string>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public ResolutionStatusDisplayResolver(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
        }

        public string Resolve(Resolution source, object destination, string destMember, ResolutionContext context)
        {
            return LocalizationHelper.GetResolutionStatusDisplay(source.Status, _localizer);
        }
    }

    /// <summary>
    /// Custom AutoMapper value resolver for localized voting type display
    /// Provides localized text for voting type using SharedResources
    /// </summary>
    public class VotingTypeDisplayResolver : IValueResolver<Resolution, object, string>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public VotingTypeDisplayResolver(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
        }

        public string Resolve(Resolution source, object destination, string destMember, ResolutionContext context)
        {
            return LocalizationHelper.GetVotingTypeDisplay(source.VotingType, _localizer);
        }
    }

    /// <summary>
    /// Custom AutoMapper value resolver for localized member voting result display
    /// Provides localized text for member voting result using SharedResources
    /// </summary>

    public class ItemVotingResultDisplayResolver : IValueResolver<ItemVotingResult, object, string>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public ItemVotingResultDisplayResolver(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
        }

        public string Resolve(ItemVotingResult source, object destination, string destMember, ResolutionContext context)
        {
            return LocalizationHelper.GetItemVoteValueDisplay(source.VoteResult, _localizer);
        }
    }
    public class MemberItemVotingResultDisplayResolver : IValueResolver<ResolutionItemVote, object, string>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public MemberItemVotingResultDisplayResolver(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
        }

        public string Resolve(ResolutionItemVote source, object destination, string destMember, ResolutionContext context)
        {
            return LocalizationHelper.GetMemberVoteValueDisplay(source.VoteResult, _localizer);
        }
    }
    public class BoardMemberTypeDisplayResolver : IValueResolver<ResolutionMemberVote, object, string>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public BoardMemberTypeDisplayResolver(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
        }

        public string Resolve(ResolutionMemberVote source, object destination, string destMember, ResolutionContext context)
        {
            return LocalizationHelper.GetBoardMemberTypeDisplay(source.BoardMember.MemberType, _localizer);
        }
    }
    public class MemberVotingResultDisplayResolver : IValueResolver<ResolutionMemberVote, object, string>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public MemberVotingResultDisplayResolver(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
        }

        public string Resolve(ResolutionMemberVote source, object destination, string destMember, ResolutionContext context)
        {
            var memberVoteResult = VotingDomainService.CalculateResolutionMemberVoteResult(source, source.ResolutionItemVotes.Count !=0 ? source.ResolutionItemVotes.ToList() : null , source.Resolution.MemberVotingResult);
            return LocalizationHelper.GetMemberVoteValueDisplay(memberVoteResult, _localizer);
        }
    }
    /// <summary>
    /// Custom AutoMapper value resolver for localized member voting result display
    /// Provides localized text for member voting result using SharedResources
    /// </summary>
    public class ResolutionMemberVotingResultDisplayResolver : IValueResolver<Resolution, object, string>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;

        public ResolutionMemberVotingResultDisplayResolver(IStringLocalizer<SharedResources> localizer)
        {
            _localizer = localizer;
        }

        public string Resolve(Resolution source, object destination, string destMember, ResolutionContext context)
        {
            return LocalizationHelper.GetMemberVotingResultDisplay(source.MemberVotingResult, _localizer);
        }
    }
    /// <summary>
    /// Custom AutoMapper value resolver for localized user role display in resolution comments
    /// Determines if the comment creator is a Board Member in the fund associated with the resolution
    /// Returns "Board Member" for board members, or the localized role name for other roles
    /// </summary>
    public class DisplayResolverForItemVoteCommentUserRole : IValueResolver<ResolutionItemVoteComment, object, string>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly IRepositoryManager _repository;

        public DisplayResolverForItemVoteCommentUserRole(IStringLocalizer<SharedResources> localizer, IRepositoryManager repository)
        {
            _localizer = localizer;
            _repository = repository;
        }

        public string Resolve(ResolutionItemVoteComment source, object destination, string destMember, ResolutionContext context)
        {
            try
            {
                // Get the fund ID from the resolution
                
                if (source.IsBoardMemberComment)
                {
                    switch (Convert.ToInt32(source.UserRoleOrBoardMemberType))
                    {
                        case (int)BoardMemberType.Independent:
                            return _localizer[SharedResourcesKey.BoardMemberTypeIndependent];
                        case (int)BoardMemberType.NotIndependent:
                            return _localizer[SharedResourcesKey.BoardMemberTypeNotIndependent];
                        default:
                            return "";
                    }
                }

                // If not a board member, get the user's role in the fund
                return   LocalizationHelper.GetUserRoleDisplay(source.UserRoleOrBoardMemberType, _localizer);
            }
            catch (Exception)
            {
                // Fallback to empty string on error
                return string.Empty;
            }
        }
     }

    /// <summary>
    /// Custom AutoMapper value resolver for localized user role display in resolution member vote comments
    /// Determines if the comment creator is a Board Member in the fund associated with the resolution
    /// Returns "Board Member" for board members, or the localized role name for other roles
    /// </summary>
    public class DisplayResolverForMemberVoteCommentUserRole : IValueResolver<ResolutionMemberVoteComment, object, string>
    {
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly IRepositoryManager _repository;

        public DisplayResolverForMemberVoteCommentUserRole(IStringLocalizer<SharedResources> localizer, IRepositoryManager repository)
        {
            _localizer = localizer;
            _repository = repository;
        }

        public string Resolve(ResolutionMemberVoteComment source, object destination, string destMember, ResolutionContext context)
        {
            try
            {
                // Get the fund ID from the resolution
                if (source.IsBoardMemberComment)
                {
                    switch (Convert.ToInt32(source.UserRoleOrBoardMemberType))
                    {
                        case (int)BoardMemberType.Independent:
                            return _localizer[SharedResourcesKey.BoardMemberTypeIndependent];
                        case (int)BoardMemberType.NotIndependent:
                            return _localizer[SharedResourcesKey.BoardMemberTypeNotIndependent];
                        default:
                            return "";
                    }
                };
                // If not a board member, get the user's role in the fund
                // return  LocalizationHelper.GetUserRoleDisplayByRoleId((Roles)Enum.Parse(typeof(Roles), source.UserRoleOrBoardMemberType.ToString().ToLower()), _localizer);
                return LocalizationHelper.GetUserRoleDisplay(source.UserRoleOrBoardMemberType.ToString().ToLower(), _localizer);
            }
            catch (Exception)
            {
                // Fallback to empty string on error
                return string.Empty;
            }
        }
    }
}
