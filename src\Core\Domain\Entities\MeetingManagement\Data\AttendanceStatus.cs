using Domain.Entities.Base;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents an attendance status entity for storing status definitions
    /// Inherits from BaseEntity to provide primary key functionality
    /// Based on requirements for meeting attendance status management
    /// Follows the same pattern as ResolutionStatus for consistency
    /// </summary>
    public class AttendanceStatus : BaseEntity
    {
        /// <summary>
        /// Arabic name of the attendance status
        /// Required field for localization support
        /// </summary>
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// English name of the attendance status
        /// Required field for localization support
        /// </summary>
        public string NameEn { get; set; } = string.Empty;

        #region Navigation Properties

        /// <summary>
        /// Collection navigation property to MeetingAttendee entities
        /// Represents all meeting attendees with this status
        /// </summary>
        public ICollection<MeetingAttendee> MeetingAttendees { get; set; } = new List<MeetingAttendee>();

        /// <summary>
        /// Collection navigation property to AttendanceStatusHistory entities
        /// Represents all status history entries for this status
        /// </summary>
        public ICollection<AttendanceStatusHistory> AttendanceStatusHistories { get; set; } = new List<AttendanceStatusHistory>();

        #endregion
    }
}
