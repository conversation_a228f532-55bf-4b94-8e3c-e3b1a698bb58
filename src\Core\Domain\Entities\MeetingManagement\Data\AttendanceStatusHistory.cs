using Domain.Entities.Base;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents an attendance history entry for tracking detailed actions performed on meeting attendance
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Based on requirements for meeting attendance history tracking
    /// Follows the same pattern as ResolutionStatusHistory for consistency
    /// </summary>
    public class AttendanceStatusHistory : FullAuditedEntity
    {
        /// <summary>
        /// Meeting attendee identifier that this history entry belongs to
        /// Foreign key reference to MeetingAttendee entity
        /// </summary>
        public int MeetingAttendeeId { get; set; }

        /// <summary>
        /// Attendance status identifier from AttendanceStatus table
        /// Foreign key reference to AttendanceStatus entity
        /// </summary>
        public int AttendanceStatusId { get; set; }

        #region Navigation Properties

        /// <summary>
        /// Navigation property to MeetingAttendee entity
        /// Provides access to the parent meeting attendee
        /// </summary>
        [ForeignKey("MeetingAttendeeId")]
        public MeetingAttendee MeetingAttendee { get; set; } = null!;

        /// <summary>
        /// Navigation property to AttendanceStatusLookup entity
        /// Provides access to the status information
        /// </summary>
        [ForeignKey("AttendanceStatusId")]
        public AttendanceStatus AttendanceStatus { get; set; } = null!;

        #endregion
    }
}
