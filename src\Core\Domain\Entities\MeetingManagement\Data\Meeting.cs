using Domain.Entities.Base;
using Domain.Entities.FundManagement;
using Domain.Entities.MeetingManagement.Data;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents a scheduled board meeting entity
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Based on requirements in BRD for User Story 3: Schedule a New Board Meeting
    /// </summary>
    public class Meeting : FullAuditedEntity
    {
        public int FundId { get; set; }

        public string Subject { get; set; } = string.Empty;

        public int MeetingTypeId { get; set; }

        public DateOnly MeetingDate { get; set; }

        public TimeSpan StartTime { get; set; }

        public TimeSpan EndTime { get; set; }

        public MeetingLocationType LocationType { get; set; }

        public string? MeetingRoomDetails { get; set; }

        public int MeetingStatusId { get; set; } = 1;

        #region Navigation Properties

        [ForeignKey("FundId")]
        public Fund Fund { get; set; } = null!;

        [ForeignKey("MeetingStatusId")]
        public MeetingStatus MeetingStatus { get; set; } = null!;
        
        [ForeignKey("MeetingTypeId")]
        public MeetingType MeetingType { get; set; } = null!;

        public ICollection<MeetingAgendaItem> AgendaItems { get; set; } = new List<MeetingAgendaItem>();

        public ICollection<MeetingAttendee> Attendees { get; set; } = new List<MeetingAttendee>();

        public ICollection<MeetingAttachment> Attachments { get; set; } = new List<MeetingAttachment>();

        public ICollection<MeetingStatusHistory> StatusHistories { get; set; } = new List<MeetingStatusHistory>();

        #endregion
    }
}
