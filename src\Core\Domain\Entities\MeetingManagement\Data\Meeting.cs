using Domain.Entities.Base;
using Domain.Entities.FundManagement;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents a scheduled board meeting entity
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Based on requirements in BRD for User Story 3: Schedule a New Board Meeting
    /// </summary>
    public class Meeting : FullAuditedEntity
    {
        /// <summary>
        /// Fund identifier that this meeting belongs to
        /// Foreign key reference to Fund entity
        /// </summary>
        public int FundId { get; set; }

        /// <summary>
        /// Meeting subject - required field
        /// Maximum 255 characters as per BRD requirements
        /// </summary>
        public string Subject { get; set; } = string.Empty;

        /// <summary>
        /// Type of meeting (Periodic, Annual)
        /// </summary>
        public MeetingType MeetingType { get; set; }

        /// <summary>
        /// Date of the meeting
        /// Must be in the future as per business rules
        /// </summary>
        public DateTime MeetingDate { get; set; }

        /// <summary>
        /// Start time of the meeting
        /// </summary>
        public TimeSpan StartTime { get; set; }

        /// <summary>
        /// End time of the meeting
        /// Business rule: Must be after start time
        /// </summary>
        public TimeSpan EndTime { get; set; }

        /// <summary>
        /// Type of meeting location (Online or Room)
        /// </summary>
        public MeetingLocationType LocationType { get; set; }

        /// <summary>
        /// Location details - either room name or online meeting link
        /// Maximum 1024 characters to accommodate long URLs
        /// </summary>
        public string? MeetingRoomDetails { get; set; }

        /// <summary>
        /// Current status of the meeting
        /// Default is Scheduled when created
        /// </summary>
        public MeetingStatus Status { get; set; } = MeetingStatus.Scheduled;

        #region Navigation Properties

        /// <summary>
        /// Navigation property to Fund entity
        /// Provides access to the fund this meeting belongs to
        /// </summary>
        [ForeignKey("FundId")]
        public Fund Fund { get; set; } = null!;

        /// <summary>
        /// Collection navigation property to MeetingAgendaItem entities
        /// Represents all agenda items for this meeting
        /// Business rule: At least one agenda item is required
        /// </summary>
        public virtual ICollection<MeetingAgendaItem> AgendaItems { get; set; } = new List<MeetingAgendaItem>();

        /// <summary>
        /// Collection navigation property to MeetingAttendee entities
        /// Represents all attendees for this meeting
        /// </summary>
        public virtual ICollection<MeetingAttendee> Attendees { get; set; } = new List<MeetingAttendee>();

        /// <summary>
        /// Collection navigation property to MeetingAttachment entities
        /// Represents all attachments for this meeting
        /// </summary>
        public virtual ICollection<MeetingAttachment> Attachments { get; set; } = new List<MeetingAttachment>();

        #endregion
    }
}
