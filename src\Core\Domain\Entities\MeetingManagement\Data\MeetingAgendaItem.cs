using Domain.Entities.Base;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents an agenda item for a meeting
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Based on requirements in BRD for meeting agenda management
    /// Business rule: At least one agenda item is required per meeting
    /// </summary>
    public class MeetingAgendaItem : FullAuditedEntity
    {
        /// <summary>
        /// Meeting identifier that this agenda item belongs to
        /// Foreign key reference to Meeting entity
        /// </summary>
        public int MeetingId { get; set; }

        /// <summary>
        /// Subject of the agenda item - required field
        /// Maximum 255 characters as per BRD requirements
        /// </summary>
        public string ItemSubject { get; set; } = string.Empty;

        /// <summary>
        /// Optional description of the agenda item
        /// Maximum 1000 characters as per BRD requirements
        /// </summary>
        public string? ItemDescription { get; set; }

        /// <summary>
        /// Display order for sorting agenda items
        /// Used for maintaining consistent agenda item ordering
        /// </summary>
        public int DisplayOrder { get; set; } = 1;

        #region Navigation Properties

        /// <summary>
        /// Navigation property to Meeting entity
        /// Provides access to the parent meeting
        /// </summary>
        [ForeignKey("MeetingId")]
        public Meeting Meeting { get; set; } = null!;

        #endregion
    }
}
