using Domain.Entities.Base;
using Domain.Entities.Shared;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents an attachment for a meeting
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Based on requirements in BRD for meeting attachment management
    /// </summary>
    public class MeetingAttachment : FullAuditedEntity
    {
        /// <summary>
        /// Meeting identifier that this attachment belongs to
        /// Foreign key reference to Meeting entity
        /// </summary>
        public int MeetingId { get; set; }

        /// <summary>
        /// Attachment identifier
        /// Foreign key reference to Attachment entity
        /// </summary>
        public int AttachmentId { get; set; }

        #region Navigation Properties

        /// <summary>
        /// Navigation property to Meeting entity
        /// Provides access to the parent meeting
        /// </summary>
        [ForeignKey("MeetingId")]
        public Meeting Meeting { get; set; } = null!;

        /// <summary>
        /// Navigation property to Attachment entity
        /// Provides access to the file attachment details
        /// </summary>
        [Foreign<PERSON><PERSON>("AttachmentId")]
        public Attachment Attachment { get; set; } = null!;

        #endregion
    }
}
