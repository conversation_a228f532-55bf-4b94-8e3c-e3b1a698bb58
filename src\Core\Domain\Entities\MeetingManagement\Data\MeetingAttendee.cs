using Domain.Entities.Base;
using Domain.Entities.FundManagement;
using Domain.Entities.Users;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents an attendee for a meeting
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Based on requirements in BRD for meeting attendee management
    /// </summary>
    public class MeetingAttendee : FullAuditedEntity
    {    
        public int MeetingId { get; set; }
        public int? FundManagerId { get; set; }
        public int? LegalCouncilId { get; set; }
        public int? BoardMemberId { get; set; }
        public int? BoardSecretaryId { get; set; }

        /// <summary>
        /// Current attendance status identifier
        /// Foreign key reference to AttendanceStatus entity
        /// Default is 1 (NoResponse) when created
        /// </summary>
        public int AttendanceStatusId { get; set; } = 1;

        #region Navigation Properties

        [ForeignKey("MeetingId")]
        public Meeting Meeting { get; set; } = null!;

        [ForeignKey("FundManagerId")]
        public FundManager? FundManager { get; set; }

        [ForeignKey("LegalCouncilId")]
        public User? LegalCouncil { get; set; }

        [ForeignKey("BoardSecretaryId")]
        public FundBoardSecretary? BoardSecretary { get; set; }

        [ForeignKey("BoardMemberId")]
        public BoardMember? BoardMember { get; set; }

        /// <summary>
        /// Navigation property to AttendanceStatusLookup entity
        /// Provides access to the attendance status information
        /// </summary>
        [ForeignKey("AttendanceStatusId")]
        public AttendanceStatus AttendanceStatus { get; set; } = null!;

        /// <summary>
        /// Collection navigation property to AttendanceStatusHistory entities
        /// Represents all status history entries for this attendee
        /// </summary>
        public virtual ICollection<AttendanceStatusHistory> AttendanceStatusHistories { get; set; } = new List<AttendanceStatusHistory>();

        #endregion
    }
}
