using Domain.Entities.Base;
using Domain.Entities.FundManagement;
using Domain.Entities.Users;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents an attendee for a meeting
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Based on requirements in BRD for meeting attendee management
    /// </summary>
    public class MeetingAttendee : FullAuditedEntity
    {    
        public int MeetingId { get; set; }
        public int? FundManagerId { get; set; }
        public int? LegalCouncilId { get; set; }
        public int? BoardMemberId { get; set; }
        public int? BoardSecretaryId { get; set; }
        public bool IsRequired { get; set; } = true;
        public bool IsPresent { get; set; }
        public AttendanceStatus AttendanceStatus { get; set; } = AttendanceStatus.NoResponse;

        #region Navigation Properties

        [ForeignKey("MeetingId")]
        public Meeting Meeting { get; set; } = null!;

        [ForeignKey("FundManagerId")]
        public FundManager? FundManager { get; set; }

        [ForeignKey("LegalCouncilId")]
        public User? LegalCouncil { get; set; }

        [ForeignKey("BoardSecretaryId")]
        public FundBoardSecretary? BoardSecretary { get; set; }

        [ForeignKey("BoardMemberId")]
        public BoardMember? BoardMember { get; set; }

        #endregion
    }
}
