﻿using Domain.Entities.Base;
using Domain.Entities.Shared;
using Domain.Entities.Users;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities.MeetingManagement.Data
{
    public class MeetingMinutes : FullAuditedEntity
    {
        public int MeetingId { get; set; }           
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;    
        public int AttachmentId { get; set; }

        #region Navigation Properties

        [ForeignKey("MeetingId")]
        public Meeting Meeting { get; set; } = null!;
        
        [ForeignKey("AttachmentId")]
        public Attachment Attachment { get; set; } = null!;

        #endregion

    }
}
