﻿using Domain.Entities.Base;
using Domain.Entities.Users;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities.MeetingManagement.Data
{
    public class MeetingNote : FullAuditedEntity
    {
        public int MeetingId { get; set; }

        public string? Note { get; set; }

        public int? ParentNoteId { get; set; }


        #region Navigation Properties

        [ForeignKey("MeetingId")]
        public Meeting Meeting { get; set; } = null!;

        [ForeignKey("ParentNoteId")]
        public MeetingNote? ParentNote { get; set; }
        public ICollection<MeetingNote> Replies { get; set; } = new List<MeetingNote>();

        #endregion
    }
}
