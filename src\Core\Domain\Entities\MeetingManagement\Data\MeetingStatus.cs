using Domain.Entities.Base;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents a meeting status entity for storing status definitions
    /// Inherits from BaseEntity to provide primary key functionality
    /// Based on requirements for meeting status management
    /// Follows the same pattern as ResolutionStatus for consistency
    /// </summary>
    public class MeetingStatus : BaseEntity
    {
        /// <summary>
        /// Arabic name of the meeting status
        /// Required field for localization support
        /// </summary>
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// English name of the meeting status
        /// Required field for localization support
        /// </summary>
        public string NameEn { get; set; } = string.Empty;

        #region Navigation Properties

        /// <summary>
        /// Collection navigation property to Meeting entities
        /// Represents all meetings with this status
        /// </summary>
        public virtual ICollection<Meeting> Meetings { get; set; } = new List<Meeting>();

        /// <summary>
        /// Collection navigation property to MeetingStatusHistory entities
        /// Represents all status history entries for this status
        /// </summary>
        public virtual ICollection<MeetingStatusHistory> MeetingStatusHistories { get; set; } = new List<MeetingStatusHistory>();

        #endregion
    }
}
