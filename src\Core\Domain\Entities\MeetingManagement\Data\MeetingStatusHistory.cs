using Domain.Entities.Base;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents a meeting history entry for tracking detailed actions performed on meetings
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Based on requirements for meeting history tracking
    /// Follows the same pattern as ResolutionStatusHistory for consistency
    /// </summary>
    public class MeetingStatusHistory : FullAuditedEntity
    {
        /// <summary>
        /// Meeting identifier that this history entry belongs to
        /// Foreign key reference to Meeting entity
        /// </summary>
        public int MeetingId { get; set; }

        /// <summary>
        /// Meeting status identifier from MeetingStatus table
        /// Foreign key reference to MeetingStatus entity
        /// </summary>
        public int MeetingStatusId { get; set; }


        #region Navigation Properties

        /// <summary>
        /// Navigation property to Meeting entity
        /// Provides access to the parent meeting
        /// </summary>
        [ForeignKey("MeetingId")]
        public Meeting Meeting { get; set; } = null!;

        /// <summary>
        /// Navigation property to MeetingStatusLookup entity
        /// Provides access to the status information
        /// </summary>
        [ForeignKey("MeetingStatusId")]
        public MeetingStatus MeetingStatus { get; set; } = null!;

        #endregion
    }
}
