using Domain.Entities.Base;
using Domain.Entities.Shared;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents an attachment for a meeting time proposal
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Based on requirements in BRD for proposal attachment management
    /// Business rule: Only PDF files are allowed for proposal attachments
    /// </summary>
    public class MeetingTimeProposalAttachment : FullAuditedEntity
    {
        /// <summary>
        /// Meeting time proposal identifier that this attachment belongs to
        /// Foreign key reference to MeetingTimeProposal entity
        /// </summary>
        public int MeetingsProposalId { get; set; }

        /// <summary>
        /// Attachment identifier
        /// Foreign key reference to Attachment entity
        /// </summary>
        public int AttachmentId { get; set; }


        #region Navigation Properties

        /// <summary>
        /// Navigation property to MeetingTimeProposal entity
        /// Provides access to the parent proposal
        /// </summary>
        [ForeignKey("MeetingsProposalId")]
        public MeetingsProposal MeetingsProposal { get; set; } = null!;

        /// <summary>
        /// Navigation property to Attachment entity
        /// Provides access to the file attachment details
        /// </summary>
        [ForeignKey("AttachmentId")]
        public Attachment Attachment { get; set; } = null!;

        #endregion
    }
}
