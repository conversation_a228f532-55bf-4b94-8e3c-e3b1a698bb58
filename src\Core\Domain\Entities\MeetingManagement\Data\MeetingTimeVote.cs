using Domain.Entities.Base;
using Domain.Entities.FundManagement;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents a vote cast by a board member on a proposed meeting time
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Based on requirements in BRD for User Story 2: Vote on Proposed Meeting Times
    /// Business rule: One vote per user per proposal
    /// </summary>
    public class MeetingTimeVote : FullAuditedEntity
    {
        /// <summary>
        /// Meeting time proposal identifier that this vote belongs to
        /// Foreign key reference to MeetingTimeProposal entity
        /// </summary>
        public int MeetingsProposalId { get; set; }

        /// <summary>
        /// User identifier for the board member who cast the vote
        /// Foreign key reference to User entity
        /// Business rule: Must be a board member of the fund
        /// </summary>
        public int BoardMemberId { get; set; }

        /// <summary>
        /// Proposed date identifier that this vote is for
        /// Foreign key reference to ProposedDate entity
        /// </summary>
        public int ProposedDateId { get; set; }


        #region Navigation Properties

        /// <summary>
        /// Navigation property to MeetingTimeProposal entity
        /// Provides access to the parent proposal
        /// </summary>
        [ForeignKey("MeetingsProposalId")]
        public MeetingsProposal MeetingsProposal { get; set; } = null!;

        /// <summary>
        /// Navigation property to User entity
        /// Provides access to the board member who cast the vote
        /// </summary>
        [ForeignKey("BoardMemberId")]
        public BoardMember BoardMember { get; set; } = null!;

        /// <summary>
        /// Navigation property to ProposedDate entity
        /// Provides access to the specific proposed date/time that was voted for
        /// </summary>
        [ForeignKey("ProposedDateId")]
        public ProposedDate ProposedDate { get; set; } = null!;

        #endregion
    }
}
