using Domain.Entities.Base;
using Domain.Entities.FundManagement;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents a meeting time proposal entity for proposing multiple meeting times to board members
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Based on requirements in BRD for User Story 1: Propose Meeting Times for Voting
    /// </summary>
    public class MeetingsProposal : FullAuditedEntity
    {
        /// <summary>
        /// Fund identifier that this proposal belongs to
        /// Foreign key reference to Fund entity
        /// </summary>
        public int FundId { get; set; }

        /// <summary>
        /// Meeting subject - required field
        /// Maximum 255 characters as per BRD requirements
        /// </summary>
        public string Subject { get; set; } = string.Empty;

        /// <summary>
        /// Optional description of the meeting proposal
        /// Maximum 1000 characters as per BRD requirements
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Current status of the proposal
        /// Default is UnderVoting when created
        /// </summary>
        public MeetingTimeProposalStatus Status { get; set; } = MeetingTimeProposalStatus.UnderVoting;

        #region Navigation Properties

        /// <summary>
        /// Navigation property to Fund entity
        /// Provides access to the fund this proposal belongs to
        /// </summary>
        [ForeignKey("FundId")]
        public Fund Fund { get; set; } = null!;

        /// <summary>
        /// Collection navigation property to ProposedDate entities
        /// Represents all proposed date/time options for this proposal
        /// Business rule: Must have at least 1 and maximum 4 proposed times
        /// </summary>
        public virtual ICollection<ProposedDate> ProposedDates { get; set; } = new List<ProposedDate>();

        /// <summary>
        /// Collection navigation property to MeetingTimeProposalAttachment entities
        /// Represents all attachments for this proposal
        /// Business rule: Attachments must be PDF files only
        /// </summary>
        public virtual ICollection<MeetingTimeProposalAttachment> Attachments { get; set; } = new List<MeetingTimeProposalAttachment>();

        /// <summary>
        /// Collection navigation property to MeetingTimeVote entities
        /// Represents all votes cast on this proposal
        /// </summary>
        public virtual ICollection<MeetingTimeVote> Votes { get; set; } = new List<MeetingTimeVote>();

        #endregion
    }
}
