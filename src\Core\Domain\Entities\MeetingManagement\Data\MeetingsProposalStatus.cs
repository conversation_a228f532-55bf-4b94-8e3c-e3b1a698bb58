using Domain.Entities.Base;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents a meeting time proposal status entity for storing status definitions
    /// Inherits from BaseEntity to provide primary key functionality
    /// Based on requirements for meeting time proposal status management
    /// Follows the same pattern as ResolutionStatus for consistency
    /// </summary>
    public class MeetingsProposalStatus : BaseEntity
    {
        /// <summary>
        /// Arabic name of the meeting time proposal status
        /// Required field for localization support
        /// </summary>
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// English name of the meeting time proposal status
        /// Required field for localization support
        /// </summary>
        public string NameEn { get; set; } = string.Empty;

        #region Navigation Properties

        /// <summary>
        /// Collection navigation property to MeetingsProposal entities
        /// Represents all meeting time proposals with this status
        /// </summary>
        public virtual ICollection<MeetingsProposal> MeetingsProposals { get; set; } = new List<MeetingsProposal>();

        /// <summary>
        /// Collection navigation property to MeetingTimeProposalStatusHistory entities
        /// Represents all status history entries for this status
        /// </summary>
        public virtual ICollection<MeetingsProposalStatusHistory> MeetingsProposalStatusHistories { get; set; } = new List<MeetingsProposalStatusHistory>();

        #endregion
    }
}
