using Domain.Entities.Base;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents a meeting time proposal history entry for tracking detailed actions performed on meeting time proposals
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Based on requirements for meeting time proposal history tracking
    /// Follows the same pattern as ResolutionStatusHistory for consistency
    /// </summary>
    public class MeetingsProposalStatusHistory : FullAuditedEntity
    {
        /// <summary>
        /// Meeting time proposal identifier that this history entry belongs to
        /// Foreign key reference to MeetingsProposal entity
        /// </summary>
        public int MeetingsProposalId { get; set; }

        /// <summary>
        /// Meeting time proposal status identifier from MeetingTimeProposalStatus table
        /// Foreign key reference to MeetingTimeProposalStatus entity
        /// </summary>
        public int MeetingsProposalStatusId { get; set; }
  

        #region Navigation Properties

        /// <summary>
        /// Navigation property to MeetingsProposal entity
        /// Provides access to the parent meeting time proposal
        /// </summary>
        [ForeignKey("MeetingsProposalId")]
        public MeetingsProposal MeetingsProposal { get; set; } = null!;

        /// <summary>
        /// Navigation property to MeetingTimeProposalStatusLookup entity
        /// Provides access to the status information
        /// </summary>
        [ForeignKey("MeetingsProposalStatusId")]
        public MeetingsProposalStatus MeetingsProposalStatus { get; set; } = null!;

        #endregion
    }
}
