using Domain.Entities.Base;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Represents a proposed date/time option for a meeting time proposal
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Based on requirements in BRD for proposed date management
    /// </summary>
    public class ProposedDate : FullAuditedEntity
    {
        /// <summary>
        /// Meeting time proposal identifier that this proposed date belongs to
        /// Foreign key reference to MeetingTimeProposal entity
        /// </summary>
        public int MeetingsProposalId { get; set; }

        /// <summary>
        /// Proposed date and time for the meeting
        /// Must be in the future as per business rules
        /// </summary>
        public DateTime ProposedDateTime { get; set; }

        #region Navigation Properties

        /// <summary>
        /// Navigation property to MeetingTimeProposal entity
        /// Provides access to the parent proposal
        /// </summary>
        [ForeignKey("MeetingsProposalId")]
        public MeetingsProposal MeetingsProposal { get; set; } = null!;

        /// <summary>
        /// Collection navigation property to MeetingTimeVote entities
        /// Represents all votes cast for this specific proposed date
        /// </summary>
        public virtual ICollection<MeetingTimeVote> Votes { get; set; } = new List<MeetingTimeVote>();

        #endregion
    }
}
