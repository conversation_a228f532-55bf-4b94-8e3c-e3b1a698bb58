using System.ComponentModel;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Enumeration representing the attendance status of a meeting attendee
    /// Based on requirements for meeting attendance tracking
    /// </summary>
    public enum AttendanceStatus
    {
        /// <summary>
        /// Attendee has not responded to the invitation
        /// Arabic: لم يرد
        /// </summary>
        [Description("No Response")]
        NoResponse = 1,

        /// <summary>
        /// Attendee has accepted the invitation
        /// Arabic: موافق
        /// </summary>
        [Description("Accepted")]
        Accepted = 2,

        /// <summary>
        /// Attendee has declined the invitation
        /// Arabic: رفض
        /// </summary>
        [Description("Declined")]
        Declined = 3,

        /// <summary>
        /// Attendee is tentatively attending
        /// Arabic: مؤقت
        /// </summary>
        [Description("Tentative")]
        Tentative = 4,

        /// <summary>
        /// Attendee attended the meeting
        /// Arabic: حضر
        /// </summary>
        [Description("Attended")]
        Attended = 5,

        /// <summary>
        /// Attendee did not attend the meeting
        /// Arabic: لم يحضر
        /// </summary>
        [Description("Did Not Attend")]
        DidNotAttend = 6
    }
}
