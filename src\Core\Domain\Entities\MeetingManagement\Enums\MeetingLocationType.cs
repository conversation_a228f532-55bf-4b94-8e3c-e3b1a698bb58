using System.ComponentModel;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Enumeration representing the location type of a meeting
    /// Based on requirements in BRD for meeting location management
    /// </summary>
    public enum MeetingLocationType
    {
        /// <summary>
        /// Online meeting (virtual)
        /// Arabic: أونلاين
        /// </summary>
        [Description("Online")]
        Online = 1,

        /// <summary>
        /// Physical meeting room
        /// Arabic: قاعة اجتماعات
        /// </summary>
        [Description("Room")]
        Room = 2
    }
}
