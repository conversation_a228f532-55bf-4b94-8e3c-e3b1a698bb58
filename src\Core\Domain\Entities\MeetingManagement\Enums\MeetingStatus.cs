using System.ComponentModel;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Enumeration representing the status of a meeting
    /// Based on requirements in BRD for meeting lifecycle management
    /// </summary>
    public enum MeetingStatus
    {
        /// <summary>
        /// Meeting is scheduled but not started yet
        /// Arabic: مجدول
        /// </summary>
        [Description("Scheduled")]
        Scheduled = 1,

        /// <summary>
        /// Meeting has not started yet (upcoming)
        /// Arabic: لم يبدأ بعد
        /// </summary>
        [Description("Not Started")]
        NotStarted = 2,

        /// <summary>
        /// Meeting is currently in progress
        /// Arabic: جاري
        /// </summary>
        [Description("Ongoing")]
        Ongoing = 3,

        /// <summary>
        /// Meeting has been completed
        /// Arabic: مكتمل
        /// </summary>
        [Description("Completed")]
        Completed = 4,

        /// <summary>
        /// Meeting has been cancelled
        /// Arabic: ملغي
        /// </summary>
        [Description("Cancelled")]
        Cancelled = 5
    }
}
