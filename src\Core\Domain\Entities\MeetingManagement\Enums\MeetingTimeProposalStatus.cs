using System.ComponentModel;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Enumeration representing the status of a meeting time proposal
    /// Based on requirements in BRD for meeting time proposal workflow
    /// </summary>
    public enum MeetingTimeProposalStatus
    {
        /// <summary>
        /// Proposal is under voting - board members can vote
        /// Arabic: تحت التصويت
        /// </summary>
        [Description("Under Voting")]
        UnderVoting = 1,

        /// <summary>
        /// Voting is completed - final time has been determined
        /// Arabic: مكتمل
        /// </summary>
        [Description("Completed")]
        Completed = 2
    }
}
