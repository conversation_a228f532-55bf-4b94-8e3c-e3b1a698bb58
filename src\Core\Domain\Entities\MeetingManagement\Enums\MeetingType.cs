using System.ComponentModel;

namespace Domain.Entities.MeetingManagement
{
    /// <summary>
    /// Enumeration representing the type of meeting
    /// Based on requirements in BRD for meeting type classification
    /// </summary>
    public enum MeetingType
    {

        /// <summary>
        /// Periodic
        /// Arabic: اجتماع دوري
        /// </summary>
        [Description("Periodic")]
        Periodic = 1,

        /// <summary>
        /// Annual meeting
        /// Arabic: اجتماع سنوي
        /// </summary>
        [Description("Annual")]
        Annual = 2,  
    }
}
