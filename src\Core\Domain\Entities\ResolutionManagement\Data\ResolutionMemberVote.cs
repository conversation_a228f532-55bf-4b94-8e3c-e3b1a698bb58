using Domain.Entities.Base;
using Domain.Entities.FundManagement;
using Domain.Entities.ResolutionManagement.Enums;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.ResolutionManagement
{
    /// <summary>
    /// Represents a vote cast by a board member on a resolution
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Based on requirements for resolution member voting system
    /// </summary>
    public class ResolutionMemberVote : FullAuditedEntity
    {
        /// <summary>
        /// Resolution identifier that this vote belongs to
        /// Foreign key reference to Resolution entity
        /// </summary>
        public int ResolutionId { get; set; }

        /// <summary>
        /// Board member identifier who cast this vote
        /// Foreign key reference to BoardMember entity
        /// </summary>
        public int BoardMemberID { get; set; }

        /// <summary>
        /// The vote result (NotVotedYet = 0, Accept = 1, Reject = 2)
        /// Represents the member's voting decision
        /// </summary>
        public VoteResult VoteResult { get; set; } = VoteResult.NotVotedYet;

        /// <summary>
        /// Navigation property to Resolution entity
        /// Provides access to the resolution being voted on
        /// </summary>
        [ForeignKey("ResolutionId")]
        public virtual Resolution Resolution { get; set; } = null!;

        /// <summary>
        /// Navigation property to BoardMember entity
        /// Provides access to the board member who cast the vote
        /// </summary>
        [ForeignKey("BoardMemberID")]
        public virtual BoardMember BoardMember { get; set; } = null!;

        /// <summary>
        /// Collection navigation property to ResolutionMemberVoteHistory entities
        /// Represents the history of status changes for this vote
        /// </summary>
        public virtual ICollection<ResolutionMemberVoteStatusHistory> ResolutionMemberVoteStatusHistories { get; set; } = new List<ResolutionMemberVoteStatusHistory>();

        /// <summary>
        /// Collection navigation property to ResolutionMemberVoteComment entities
        /// Represents comments associated with this vote
        /// </summary>
        public virtual ICollection<ResolutionMemberVoteComment> ResolutionMemberVoteComments { get; set; } = new List<ResolutionMemberVoteComment>();

        /// <summary>
        /// Collection navigation property to ResolutionItemVote entities
        /// Represents votes on individual resolution items by this member
        /// </summary>
        public virtual ICollection<ResolutionItemVote> ResolutionItemVotes { get; set; } = new List<ResolutionItemVote>();

        /// <summary>
        /// Checks if the member has voted (not in NotVotedYet state)
        /// </summary>
        /// <returns>True if member has cast a vote, false otherwise</returns>
        public bool HasVoted()
        {
            return VoteResult != VoteResult.NotVotedYet;
        }

        
    }
}
