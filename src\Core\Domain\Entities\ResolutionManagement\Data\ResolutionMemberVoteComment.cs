using Domain.Entities.Base;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities.ResolutionManagement
{
    /// <summary>
    /// Represents a comment associated with a resolution member vote
    /// Inherits from FullAuditedEntity to provide comprehensive audit trail functionality
    /// Allows board members to provide explanations or notes about their votes
    /// </summary>
    public class ResolutionMemberVoteComment : FullAuditedEntity
    {
        /// <summary>
        /// Comment text providing additional information about the item vote
        /// Can be used to explain the reasoning behind the vote decision for this specific item
        /// </summary>
        public string UserRoleOrBoardMemberType { get; set; }
        public bool IsBoardMemberComment { get; set; }

        /// <summary>
        /// Resolution member vote identifier that this comment belongs to
        /// Foreign key reference to ResolutionMemberVote entity
        /// </summary>
        public int ResolutionMemberVoteID { get; set; }

        /// <summary>
        /// Comment text providing additional information about the vote
        /// Can be used to explain the reasoning behind the vote decision
        /// </summary>
        public string? Comment { get; set; } 

        /// <summary>
        /// Navigation property to ResolutionMemberVote entity
        /// Provides access to the vote this comment belongs to
        /// </summary>
        [ForeignKey("ResolutionMemberVoteID")]
        public virtual ResolutionMemberVote ResolutionMemberVote { get; set; } = null!;

 
 
    }
}
