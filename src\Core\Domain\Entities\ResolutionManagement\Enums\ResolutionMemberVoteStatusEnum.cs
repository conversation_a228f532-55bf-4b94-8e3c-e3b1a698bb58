using System.ComponentModel;

namespace Domain.Entities.ResolutionManagement
{
    /// <summary>
    /// Enumeration representing the possible vote results for resolution member voting
    /// Based on business requirements for the voting system
    /// Values: NotVotedYet (0), Accept (1), Reject (2)
    /// </summary>
    public enum ResolutionMemberVoteStatusEnum
    {
        /// <summary>
        /// Member has not voted yet
        /// Arabic: لم يصوت بعد
        /// </summary>
        [Description("Not Voted Yet")]

        NotVotedYet = 1,

        /// <summary>
        /// Member accepts/approves the resolution or item
        /// Arabic: تم التصويت
        /// </summary>
        [Description("Voted")]
        Voted = 2,

        /// <summary>
        /// Member rejects the resolution or item
        /// Arabic: اعادة التصويت
        /// </summary>
        [Description("RequestRevote")]
        RequestRevote = 3
    }
   
}
