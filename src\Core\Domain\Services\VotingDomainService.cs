using Domain.Entities.FundManagement;
using Domain.Entities.ResolutionManagement;
using Domain.Entities.ResolutionManagement.Enums;
using Domain.Entities.Shared;
using Microsoft.AspNetCore.Identity;

namespace Domain.Services
{
    /// <summary>
    /// Domain service for voting business rules and operations
    /// Encapsulates complex business logic related to resolution voting
    /// Based on voting requirements for resolution management system
    /// </summary>
    public class VotingDomainService
    {
        ///// <summary>
        ///// Calculates voting results for a resolution based on voting methodology
        ///// </summary>
        ///// <param name="resolution">Resolution to calculate results for</param>
        ///// <param name="votes">All active votes for the resolution</param>
        ///// <param name="eligibleMembers">All eligible board members for voting</param>
        ///// <returns>Voting result with approval status and details</returns>
        public static ResolutionVotingResult CalculateResolutionResult(Resolution resolution, int items, IEnumerable<ResolutionMemberVote> votes)
        {
            var result = new ItemVoteResult();
            // If resolution has items, calculate based on item voting
            if (items != 0)
            {
                return CalculateResolutionVoteFromItemVotes(resolution.ResolutionItems.SelectMany(c => c.ResolutionItemVotes), resolution.MemberVotingResult);
            }

            // Calculate based on resolution-level voting
            return CalculateDirectResolutionResult(resolution, votes);
        }
        private static ResolutionVotingResult CalculateDirectResolutionResult(Resolution resolution, IEnumerable<ResolutionMemberVote> resolutionVotes)
        {
            var result = new ResolutionVotingResult();
            var hasPendingVote = resolutionVotes.Where(v => v.VoteResult == VoteResult.NotVotedYet).Any();
            if (!hasPendingVote)
            {
                result.EligibleVoters = resolutionVotes.Count(v => v.VoteResult != VoteResult.NotEligibleToVote);
                result.ApproveVotes = resolutionVotes.Count(v => v.VoteResult == VoteResult.Accept);
                result.RejectVotes = resolutionVotes.Count(v => v.VoteResult == VoteResult.Reject);
                result.VotesCast = result.ApproveVotes + result.RejectVotes;
                if (resolution.VotingType == VotingType.AllMembers)
                {
                    // Unanimous approval required
                    result.IsApproved = result.RejectVotes == 0;
                }
                else // Majority
                {
                    // Majority approval required
                    result.IsApproved = result.ApproveVotes > (result.EligibleVoters / 2.0);
                }
                return result;
            }
            else
            {
                return result;
            }
        }
        public static ResolutionVotingResult CalculateResolutionVoteFromItemVotes(IEnumerable<ResolutionItemVote> itemVotes, MemberVotingResult memberVotingResult)
        {
            var result = new ResolutionVotingResult();
            var hasPendingVote = itemVotes.Where(v => v.VoteResult == VoteResult.NotVotedYet).Any();
            if (!hasPendingVote)
            {
                var acceptedItems = itemVotes.Count(iv => iv.VoteResult == VoteResult.Accept);
                var totalVotedItems = itemVotes.Count();
                result.EligibleVoters = itemVotes.Count(v => v.VoteResult != VoteResult.NotEligibleToVote);
                result.ApproveVotes = itemVotes.Count(v => v.VoteResult == VoteResult.Accept);
                result.RejectVotes = itemVotes.Count(v => v.VoteResult == VoteResult.Reject);
                result.VotesCast = result.ApproveVotes + result.RejectVotes;
                if (memberVotingResult == MemberVotingResult.AllItems)
                {
                    // Unanimous approval required
                    result.IsApproved = result.RejectVotes == 0;
                }
                else // Majority
                {
                    // Majority approval required
                    result.IsApproved = result.ApproveVotes > (result.EligibleVoters / 2.0);
                }
                return result;
            }
            else
            {
                return result;
            }
        }
        public static ItemVotingResult CalculateResolutionItemVoteResult(ResolutionItem item, List<ResolutionItemVote> itemVotes)
        {
            var result = new ItemVotingResult();
            result.EligibleVoters = itemVotes.Count() - itemVotes.Where(c => c.VoteResult == VoteResult.NotEligibleToVote).Count();
            var approveVotes = itemVotes.Count(v => v.VoteResult == VoteResult.Accept);
            var rejectVotes = itemVotes.Count(v => v.VoteResult == VoteResult.Reject);
            var notVotedYetCount = itemVotes.Count(v => v.VoteResult == VoteResult.NotVotedYet);
            result.VotesCast = itemVotes.Where(c => c.VoteResult == VoteResult.Accept || c.VoteResult == VoteResult.Reject).Count();
            result.ApproveVotes = approveVotes;
            result.PendingVotes = notVotedYetCount;
            result.RejectVotes = rejectVotes;
            return result;
        }
        public static VoteResult CalculateResolutionMemberVoteResult(ResolutionMemberVote memberVote, List<ResolutionItemVote>? memberVotes, MemberVotingResult resolutionMemberVotingResult)
        {
            
            if (memberVotes != null)
            {
                var allElgigibleVoteCount = memberVotes.Where(c => c.VoteResult != VoteResult.NotEligibleToVote).Count();
                var notVotedYet = memberVotes.Where(c => c.VoteResult == VoteResult.NotVotedYet).Any();
                if (notVotedYet)
                {
                    return VoteResult.NotVotedYet;
                }

                if (resolutionMemberVotingResult == MemberVotingResult.AllItems)
                {
                    if (memberVotes.Any(c => c.VoteResult == VoteResult.Reject))
                        return VoteResult.Reject;
                    else
                        return VoteResult.Accept;
                }
                else
                {
                    if (memberVotes.Where(c => c.VoteResult == VoteResult.Accept).Count() > (allElgigibleVoteCount / 2.0))
                        return VoteResult.Accept;
                    else
                        return VoteResult.Reject;
                }

                #region AS IS

                //if (notVotedYet)
                //{
                //    return VoteResult.NotVotedYet;
                //}
                //else if (resolutionMemberVotingResult == MemberVotingResult.AllItems && !memberVotes.All(c => c.VoteResult == VoteResult.Accept))
                //{
                //    return VoteResult.Reject;
                //}
                //else if (resolutionMemberVotingResult == MemberVotingResult.MajorityOfItems && memberVotes.Where(c => c.VoteResult == VoteResult.Accept).Count() < (allElgigibleVoteCount / 2.0))
                //{
                //    return VoteResult.Reject;
                //}
                //else
                //{
                //    return VoteResult.Accept;
                //}

                #endregion
            }
            else
            {
                return memberVote.VoteResult;
            }
        }
        public static List<ResolutionMemberVote> CreateInitialVotingRecords(Resolution resolution,IEnumerable<BoardMember> eligibleMembers,IEnumerable<ResolutionItemConflict>? conflicts)
        {
            var memberVotes = new List<ResolutionMemberVote>();
            

            foreach (var member in eligibleMembers)
            {
                var memberVote = new ResolutionMemberVote
                {
                    ResolutionId = resolution.Id,
                    BoardMemberID = member.Id,
                    VoteResult = VoteResult.NotVotedYet,
                    ResolutionItemVotes = new List<ResolutionItemVote>()
                };
                memberVote.ResolutionMemberVoteStatusHistories.Add(new ResolutionMemberVoteStatusHistory
                {
                    StatusID = (int)ResolutionMemberVoteStatusEnum.NotVotedYet,
                });
                if (resolution.ResolutionItems.Count != 0 )
                {
                    var conflictsByItem = conflicts.Any() ?  conflicts.GroupBy(c => c.ResolutionItemId).ToDictionary(g => g.Key, g => g.ToList()) : null;
                    // Create ResolutionItemVote for each resolution item
                    foreach (var item in resolution.ResolutionItems)
                    {
                        var hasConflict = conflictsByItem.Any() ? conflictsByItem.ContainsKey(item.Id) && conflictsByItem[item.Id].Any(c => c.BoardMemberId == member.Id) : false ;

                        var itemVote = new ResolutionItemVote
                        {
                            ResolutionItemId = item.Id,
                            VoteResult = hasConflict ? VoteResult.NotEligibleToVote : VoteResult.NotVotedYet
                        };

                        memberVote.ResolutionItemVotes.Add(itemVote);
                    }
                }             
                memberVotes.Add(memberVote);
            }
            return memberVotes;
        }
    }

    /// <summary>
    /// Represents the result of voting calculation for a resolution
    /// </summary>
    public class ResolutionVotingResult
    {
        public int EligibleVoters { get; set; }
        public bool IsApproved { get; set; }
        public int ApproveVotes { get; set; }
        public int PendingVotes { get; set; }
        public int RejectVotes { get; set; }
        public int VotesCast { get; set; }
        public ItemVoteResult VoteResult
        {
            get
            {
                if (!IsApproved)
                    return ItemVoteResult.Rejected;
                else if (IsApproved)
                    return ItemVoteResult.Accepted;
                else
                    return ItemVoteResult.VotingInProgress;
            }
        }
    }

    /// <summary>
    /// Represents the voting result for a single resolution item
    /// </summary>
    public class ItemVotingResult
    {
         
       
        public int EligibleVoters { get; set; }
        public int VotesCast { get; set; }
        public int ApproveVotes { get; set; }
        public int RejectVotes { get; set; }
        public int PendingVotes { get; set; }
        public ItemVoteResult VoteResult
        {
            get
            {
                if (VotesCast < EligibleVoters)
                    return ItemVoteResult.VotingInProgress;
                else if (RejectVotes > ApproveVotes && VotesCast == EligibleVoters)
                    return ItemVoteResult.Rejected;
                else if (ApproveVotes > RejectVotes && VotesCast == EligibleVoters)
                    return ItemVoteResult.Accepted;
                else if (ApproveVotes == RejectVotes && VotesCast == EligibleVoters)
                    return ItemVoteResult.Accepted;
                else
                    return ItemVoteResult.VotingInProgress;
            }
        }
       
        
    }
}
