﻿using Domain.Entities.Products;
using Domain.Entities.Users;
using Microsoft.EntityFrameworkCore;
using Domain.Entities.Shared;
using Domain.Entities.Startegies;
using Domain.Entities.FundManagement;
using Domain.Entities.Notifications;
using Domain.Entities.ResolutionManagement;
using Domain.Entities.DocumentManagement;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.MeetingManagement;
using Domain.Entities.MeetingManagement.Data;
using Infrastructure.Data.Config.MeetingManagement;

namespace Infrastructure.Data
{
    public class AppDbContext : AuditableDbContext 
    {
        #region Fileds

        //public DbSet<Product> Products { get; set; }
        //public DbSet<DemoEntity> DemoEntities { get; set; }
        //public DbSet<Category> Categories { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<UserAuditHistory> UserAuditHistories { get; set; }
        public DbSet<Attachment> Attachments { get; set; }
        public DbSet<Strategy> Strategies { get; set; }
        public DbSet<Fund> Funds { get; set; }
        public DbSet<StatusHistory> StatusHistories { get; set; }
        public DbSet<FundStatusHistory> FundStatusHistories { get; set; }
        public DbSet<FundBoardSecretary> FundBoardSecretaries { get; set; }
        public DbSet<FundManager> FundManagers { get; set; }
        public DbSet<FundMember> FundMembers { get; set; }
        public DbSet<Notification> Notifications { get; set; }

        // Board Member Management
        public DbSet<BoardMember> BoardMembers { get; set; }

        // Resolution Management
        public DbSet<Resolution> Resolutions { get; set; }
        public DbSet<ResolutionItem> ResolutionItems { get; set; }
        public DbSet<ResolutionItemConflict> ResolutionItemConflicts { get; set; }
        public DbSet<ResolutionType> ResolutionTypes { get; set; }
        public DbSet<ResolutionAttachment> ResolutionAttachments { get; set; }
        public DbSet<ResolutionStatusHistory> ResolutionStatusHistories { get; set; }


        // Resolution Voting Management
        public DbSet<ResolutionMemberVote> ResolutionMemberVotes { get; set; }
        public DbSet<ResolutionMemberVoteStatus> ResolutionMemberVoteStatuses { get; set; }
        public DbSet<ResolutionMemberVoteStatusHistory> ResolutionMemberVoteHistories { get; set; }
        public DbSet<ResolutionMemberVoteComment> ResolutionMemberVoteComments { get; set; }
        public DbSet<ResolutionItemVote> ResolutionItemVotes { get; set; }
        public DbSet<ResolutionItemVoteComment> ResolutionItemVoteComments { get; set; }

        // Document Management
        public DbSet<Document> Documents { get; set; }
        public DbSet<DocumentCategory> DocumentCategories { get; set; }

        // Meetings Management
        public DbSet<MeetingsProposal> MeetingsProposals { get; set; }
        public DbSet<MeetingsProposalAttachment> MeetingTimeProposalAttachments { get; set; }
        public DbSet<ProposedDate> ProposedDates { get; set; }
        public DbSet<MeetingsProposalVote> MeetingTimeVotes { get; set; }
        public DbSet<Meeting> Meetings { get; set; }
        public DbSet<MeetingAgendaItem> MeetingAgendaItems { get; set; }
        public DbSet<MeetingAttendee> MeetingAttendees { get; set; }
        public DbSet<MeetingMinutes> MeetingMinutes { get; set; }
        public DbSet<MeetingNote> MeetingNotes { get; set; }
        public DbSet<MeetingAttachment> MeetingAttachments { get; set; }

        // Meeting Status Management
        public DbSet<MeetingStatus> MeetingStatuses { get; set; }
        public DbSet<MeetingsProposalStatus> MeetingsProposalStatuses { get; set; }
        public DbSet<AttendanceStatus> AttendanceStatuses { get; set; }

        // Meeting Status History Management
        public DbSet<MeetingStatusHistory> MeetingStatusHistories { get; set; }
        public DbSet<MeetingsProposalStatusHistory> MeetingsProposalStatusHistories { get; set; }
        public DbSet<AttendanceStatusHistory> AttendanceStatusHistories { get; set; }

        // Assessment Management
        public DbSet<Assessment> Assessments { get; set; }
        public DbSet<AssessmentQuestion> AssessmentQuestions { get; set; }
        public DbSet<AssessmentResponse> AssessmentResponses { get; set; }
        public DbSet<Answer> Answers { get; set; }
        public DbSet<Option> Options { get; set; }
        public DbSet<AnswerOption> AnswerOptions { get; set; }
        public DbSet<AssessmentStatusHistory> AssessmentStatusHistories { get; set; }

        #endregion

        #region Constructors

        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {

        }

        #endregion

        #region Model Configuration

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Apply entity configurations
            modelBuilder.ApplyConfigurationsFromAssembly(typeof(AppDbContext).Assembly);

            // Seed meeting status data
            MeetingStatusSeeds.SeedMeetingStatus(modelBuilder);
            MeetingsProposalStatusSeed.SeedMeetingsProposalStatus(modelBuilder);
            AttendanceStatusSeeds.SeedAttendanceStatus(modelBuilder);
        }

        #endregion
    }
}
