using Domain.Entities.AssessmentManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.Config.AssessmentManagement
{
    /// <summary>
    /// Entity Framework configuration for AssessmentResponse entity
    /// Configures navigation properties, relationships, and constraints for assessment responses
    /// Provides comprehensive configuration for board member response management
    /// </summary>
    public class AssessmentResponseEntityConfig : IEntityTypeConfiguration<AssessmentResponse>
    {
        public void Configure(EntityTypeBuilder<AssessmentResponse> builder)
        {
            // Configure primary key
            builder.HasKey(ar => ar.Id);

            // Configure required properties with constraints
            builder.Property(ar => ar.AssessmentId)
                .IsRequired()
                .HasComment("Foreign key reference to the Assessment this response is for");

            builder.Property(ar => ar.BoardMemberId)
                .IsRequired()
                .HasComment("Foreign key reference to the User (Board Member) who submitted this response");

            builder.Property(ar => ar.Status)
                .IsRequired()
                .HasDefaultValue(Domain.Entities.AssessmentManagement.Enums.ResponseStatus.Pending)
                .HasComment("Status of the response (Pending or Completed)");

            // Configure optional properties
            builder.Property(ar => ar.SubmissionDate)
                .HasComment("Date when the response was submitted");

            builder.Property(ar => ar.GeneralComments)
                .HasMaxLength(4000)
                .HasComment("General comments provided by the board member");

            // Configure relationships
            builder.HasOne(ar => ar.Assessment)
                .WithMany(a => a.Responses)
                .HasForeignKey(ar => ar.AssessmentId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_AssessmentResponses_Assessments_AssessmentId");

            builder.HasOne(ar => ar.BoardMember)
                .WithMany()
                .HasForeignKey(ar => ar.BoardMemberId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_AssessmentResponses_BoardMembers_BoardMemberId");

            // Configure one-to-many relationships
            builder.HasMany(ar => ar.Answers)
                .WithOne(a => a.Response)
                .HasForeignKey(a => a.ResponseId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Answers_AssessmentResponses_ResponseId");

            // Configure unique constraint to ensure one response per user per assessment
            builder.HasIndex(ar => new { ar.AssessmentId, ar.BoardMemberId })
                .IsUnique()
                .HasDatabaseName("IX_AssessmentResponses_AssessmentId_UserId_Unique")
                .HasFilter("[IsDeleted] = 0");

            // Configure indexes for performance optimization
            builder.HasIndex(ar => ar.AssessmentId)
                .HasDatabaseName("IX_AssessmentResponses_AssessmentId_Performance");

            builder.HasIndex(ar => ar.BoardMemberId)
                .HasDatabaseName("IX_AssessmentResponses_UserId_Performance");

            builder.HasIndex(ar => ar.Status)
                .HasDatabaseName("IX_AssessmentResponses_Status_Performance");

            builder.HasIndex(ar => ar.SubmissionDate)
                .HasDatabaseName("IX_AssessmentResponses_SubmissionDate_Performance")
                .HasFilter("[SubmissionDate] IS NOT NULL");

            // Configure table name
            builder.ToTable("AssessmentResponses");

            // Configure audit properties (inherited from FullAuditedEntity)
            builder.Property(ar => ar.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Timestamp when the response was created");

            builder.Property(ar => ar.UpdatedAt)
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Timestamp when the response was last updated");

            builder.Property(ar => ar.IsDeleted)
                .HasDefaultValue(false)
                .HasComment("Soft delete flag");

           
        }
    }
}
