using Abstraction.Contract.Repository.DocumentManagement;
using Abstraction.Contract.Repository.Fund;
using Domain.Entities.DocumentManagement;
using Domain.Entities.FundManagement;
using Infrastructure.Repository.Fund;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Repository.Catalog;

namespace Infrastructure.Data.Config.DocumentManagement
{
    /// <summary>
    /// Entity configuration for DocumentCategory
    /// </summary>
    public class DocumentCategoryConfig : IEntityTypeConfiguration<DocumentCategory>
    {
        public void Configure(EntityTypeBuilder<DocumentCategory> builder)
        {
            // Table configuration
            builder.ToTable("DocumentCategories");

            // Primary key
            builder.HasKey(x => x.Id);

            // Properties
            builder.Property(x => x.NameAr)
                .IsRequired()
                .HasMaxLength(255);

            builder.Property(x => x.NameEn)
                .IsRequired()
                .HasMaxLength(255);

            // Relationships
            builder.HasMany(x => x.Documents)
                .WithOne(x => x.DocumentCategory)
                .HasForeignKey(x => x.DocumentCategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes
            builder.HasIndex(x => x.NameAr);
            builder.HasIndex(x => x.NameEn);
            builder.HasIndex(x => x.DisplayOrder);
        }

        public static async Task SeedData(IDocumentCategoryRepository documentCategoryRepository)
        {
            var categories = new List<DocumentCategory>
            {
                // Root categories
                new DocumentCategory
                {
                    NameAr = "شروط الصندوق واللوائح",
                    NameEn = "Fund Terms and Conditions",
                    DisplayOrder = 1,
                },
                new DocumentCategory
                {
                    NameAr = "محاضر الاجتماعات",
                    NameEn = "Minutes of Meeting",
                    DisplayOrder = 2,
                },
                new DocumentCategory
                {
                    NameAr = "تقارير",
                    NameEn = "Report",
                    DisplayOrder = 3,
                },
                new DocumentCategory
                {
                    NameAr = "اخري",
                    NameEn = "Others",
                    DisplayOrder = 4,
                },
            };
            foreach (var category in categories)
            {
                if (!await documentCategoryRepository.AnyAsync<DocumentCategory>(x => x.NameEn == category.NameEn))
                {
                    await documentCategoryRepository.AddAsync(category);
                }
            }
        }
    }
}
