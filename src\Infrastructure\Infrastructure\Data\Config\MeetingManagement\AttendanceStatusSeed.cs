using Domain.Entities.MeetingManagement;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Data.Config.MeetingManagement
{
    /// <summary>
    /// Seed data for AttendanceStatus table
    /// Provides initial status definitions for meeting attendance management
    /// </summary>
    public static class AttendanceStatusSeeds
    {
        public static void SeedAttendanceStatus(ModelBuilder modelBuilder)
        {
            var attendanceStatuses = new List<AttendanceStatus>
            {
                new AttendanceStatus
                {
                    Id = 1,
                    NameAr = "لم يرد",
                    NameEn = "No Response",
                },
                new AttendanceStatus
                {
                    Id = 2,
                    NameAr = "موافق",
                    NameEn = "Accepted",
                },
                new AttendanceStatus
                {
                    Id = 3,
                    NameAr = "رفض",
                    NameEn = "Declined",
                },
                new AttendanceStatus
                {
                    Id = 4,
                    NameAr = "مؤقت",
                    NameEn = "Tentative",
                },
                new AttendanceStatus
                {
                    Id = 5,
                    NameAr = "حاضر",
                    NameEn = "Present",
                },
                new AttendanceStatus
                {
                    Id = 6,
                    NameAr = "غائب",
                    NameEn = "Absent",
                }
            };

            modelBuilder.Entity<AttendanceStatus>().HasData(attendanceStatuses);
        }
    }
}
