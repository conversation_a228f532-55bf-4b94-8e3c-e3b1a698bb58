using Domain.Entities.MeetingManagement;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Data.Config.MeetingManagement
{
    /// <summary>
    /// Seed data for MeetingStatus table
    /// Provides initial status definitions for meeting management
    /// </summary>
    public static class MeetingStatusSeeds
    {
        public static void SeedMeetingStatus(ModelBuilder modelBuilder)
        {
            var meetingStatuses = new List<MeetingStatus>
            {
                new MeetingStatus
                {
                    Id = 1,
                    NameAr = "مجدول",
                    NameEn = "Scheduled",
                },
                new MeetingStatus
                {
                    Id = 2,
                    NameAr = "لم يبدأ بعد",
                    NameEn = "Not Started",
                },
                new MeetingStatus
                {
                    Id = 3,
                    NameAr = "جاري",
                    NameEn = "Ongoing",
                },
                new MeetingStatus
                {
                    Id = 4,
                    NameAr = "مكتمل",
                    NameEn = "Completed",
                },
                new MeetingStatus
                {
                    Id = 5,
                    NameAr = "ملغي",
                    NameEn = "Cancelled",
                },
                new MeetingStatus
                {
                    Id = 6,
                    NameAr = "مؤجل",
                    NameEn = "Postponed",
                },
                new MeetingStatus
                {
                    Id = 7,
                    NameAr = "معاد جدولته",
                    NameEn = "Rescheduled",
                }
            };

            modelBuilder.Entity<MeetingStatus>().HasData(meetingStatuses);
        }
    }
}
