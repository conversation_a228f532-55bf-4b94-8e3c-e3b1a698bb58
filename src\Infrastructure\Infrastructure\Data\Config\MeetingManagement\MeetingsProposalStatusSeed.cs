using Domain.Entities.MeetingManagement;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Data.Config.MeetingManagement
{
    /// <summary>
    /// Seed data for MeetingTimeProposalStatus table
    /// Provides initial status definitions for meeting time proposal management
    /// </summary>
    public static class MeetingsProposalStatusSeed
    {
        public static void SeedMeetingsProposalStatus(ModelBuilder modelBuilder)
        {
            var proposalStatuses = new List<MeetingsProposalStatus>
            {
                new MeetingsProposalStatus
                {
                    Id = 1,
                    NameAr = "تحت التصويت",
                    NameEn = "Under Voting",
                },
                new MeetingsProposalStatus
                {
                    Id = 2,
                    NameAr = "مكتمل",
                    NameEn = "Completed",
                }
            };

            modelBuilder.Entity<MeetingsProposalStatus>().HasData(proposalStatuses);
        }
    }
}
