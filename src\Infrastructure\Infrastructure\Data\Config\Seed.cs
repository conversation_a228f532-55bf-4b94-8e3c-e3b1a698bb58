﻿using Abstraction.Contracts.Repository;
using Domain.Entities.Users;
using Infrastructure.Data.Config.DocumentManagement;
using Infrastructure.Data.Config.MeetingManagement;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;

namespace Infrastructure.Data.Config
{
    public static class Seed
    {
        public static async Task SeedAsync(IServiceProvider serviceProvider)
        {
            var userManager = serviceProvider.GetRequiredService<UserManager<User>>();
            var roleManager = serviceProvider.GetRequiredService<RoleManager<Role>>();
            var repositoryManager = serviceProvider.GetRequiredService<IRepositoryManager>();
            await RoleConfig.SeedAsync(roleManager);
            await UserConfig.SeedSuperAdminAsync(userManager, roleManager);
            await UserConfig.SeedFundManagerAsync(userManager, roleManager);
            await UserConfig.SeedLegalCouncilAsync(userManager, roleManager);
            await UserConfig.SeedBoardSecretaryAsync(userManager, roleManager);
            await UserConfig.SeedBoardMembersAsync(userManager, roleManager);
            await StatusHistoryConfig.SeedStatusHistoryAsync(repositoryManager.StatusHistory);
            await ResolutionTypeSeed.SeedResolutionTypeAsync(repositoryManager.ResolutionTypes);
            await ResolutionStatusSeeds.SeedResolutionStatusAsync(repositoryManager.ResolutionStatuses);
            await DocumentCategoryConfig.SeedData(repositoryManager.DocumentCategoryRepository);

            // Meeting Management seed data
            await MeetingTypeConfig.SeedData(repositoryManager.MeetingTypeRepository);
            await MeetingStatusEntityConfig.SeedData(repositoryManager.MeetingStatusRepository);
            await MeetingsProposalStatusEntityConfig.SeedData(repositoryManager.MeetingsProposalStatusRepository);
            await AttendanceStatusEntityConfig.SeedData(repositoryManager.AttendanceStatusRepository);
        }
    }
}
