﻿using Abstraction.Base.Dto;
using Application.Common.Dtos;
using Domain.Entities.Base;


namespace Infrastructure.Dto.DocumentCategory
{
    public record BaseDocumentCategoryDto : LocalizedDto
    {
        public int DisplayOrder { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? DeletedAt { get; set; }
        public int? CreatedBy { get; set; }
        public int? UpdatedBy { get; set; }
        public bool? IsDeleted { get; set; }
    }
}
