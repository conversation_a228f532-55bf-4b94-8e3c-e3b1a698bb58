﻿using Abstraction.Contracts.Repository;
using Domain.Entities.Startegies;
using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;
using Shared.Behaviors;

namespace Infrastructure.Dto.Strategies
{
    public class DocumentCategoryEditValidation : AbstractValidator<DocumentCategoryEditDto>
    {
        protected IGenericRepository _repository;
        protected IStringLocalizer<SharedResources> _localizer;
        public DocumentCategoryEditValidation(IGenericRepository repository, IStringLocalizer<SharedResources> localizer)
        {
            _repository = repository;
            _localizer = localizer;
            ApplyValidationsRules();
        }
        public void ApplyValidationsRules()
        {
            RuleFor(c => c.NameAr).SetValidator(new NotEmptyAndNotNullWithMessageValidator<DocumentCategoryEditDto, string>(_localizer))
                .MaximumLength(50).WithMessage(string.Format(_localizer[SharedResourcesKey.MaxLength], 50));

            RuleFor(c => c.NameEn).SetValidator(new NotEmptyAndNotNullWithMessageValidator<DocumentCategoryEditDto, string>(_localizer))
                .MaximumLength(50).WithMessage(string.Format(_localizer[SharedResourcesKey.MaxLength], 50));


            RuleFor(c => c)
                .MustAsync(async (c, cancellation) =>
                {
                    var exists = await _repository.AnyAsync<Domain.Entities.DocumentManagement.DocumentCategory>(x => x.Id != c.Id &&
                                                                           (x.NameAr == c.NameAr || x.NameEn == c.NameEn));
                    return !exists;
                })
                .WithMessage(_localizer[SharedResourcesKey.Unique]);
        }

    }
}
