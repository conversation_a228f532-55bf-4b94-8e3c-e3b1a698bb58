﻿using AutoMapper;
using Domain.Entities.Startegies;
using Infrastructure.Dto.DocumentCategory;

namespace Infrastructure.Dto.Strategies
{
    public partial class DocumentCategoryProfile : Profile
    {
        public DocumentCategoryProfile()
        {
            CreateMap<Domain.Entities.DocumentManagement.DocumentCategory, DocumentCategoryAddDto>().ReverseMap();
            CreateMap<Domain.Entities.DocumentManagement.DocumentCategory, DocumentCategoryEditDto>().ReverseMap();
            CreateMap<Domain.Entities.DocumentManagement.DocumentCategory, BaseDocumentCategoryDto>().ReverseMap();
        }
    }
}
