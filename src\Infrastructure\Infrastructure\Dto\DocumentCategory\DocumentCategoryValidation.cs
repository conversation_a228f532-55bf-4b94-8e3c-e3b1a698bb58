﻿using Abstraction.Contracts.Repository;
using Domain.Entities.Startegies;
using FluentValidation;
using Microsoft.Extensions.Localization;
using Resources;
using Shared.Behaviors;

namespace Infrastructure.Dto.Strategies
{
    public class DocumentCategoryValidation : AbstractValidator<DocumentCategoryAddDto>
    {
        protected IGenericRepository _repository;
        protected IStringLocalizer<SharedResources> _localizer;
        public DocumentCategoryValidation(IGenericRepository repository,IStringLocalizer<SharedResources> localizer)
        {
            _repository = repository;
            _localizer = localizer;
            ApplyValidationsRules();
        }
        public void ApplyValidationsRules()
        {
          
            RuleFor(c => c.NameAr).SetValidator(new NotEmptyAndNotNullWithMessageValidator<DocumentCategoryAddDto, string>(_localizer))
                .MaximumLength(50).WithMessage(string.Format(_localizer[SharedResourcesKey.MaxLength], 50))
                .MustAsync(async (nameAr, cancellation) =>
                {
                    var exists = await _repository.AnyAsync<Domain.Entities.DocumentManagement.DocumentCategory>(s => s.NameAr == nameAr);
                    return !exists;
                })
                .WithMessage(_localizer[SharedResourcesKey.Unique]);

            RuleFor(c => c.NameEn).SetValidator(new NotEmptyAndNotNullWithMessageValidator<DocumentCategoryAddDto, string>(_localizer))
                .MaximumLength(50).WithMessage(string.Format(_localizer[SharedResourcesKey.MaxLength], 50))
                .MustAsync(async (nameEn, cancellation) =>
                {
                    var exists = await _repository.AnyAsync<Domain.Entities.DocumentManagement.DocumentCategory>(s => s.NameEn == nameEn);
                    return !exists;
                })
                .WithMessage(_localizer[SharedResourcesKey.Unique]);
        } 

    }
}
