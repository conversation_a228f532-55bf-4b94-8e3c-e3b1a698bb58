﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class _changeRelationInResponseToMember : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentResponses_Users_UserId",
                table: "AssessmentResponses");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "AssessmentResponses",
                newName: "BoardMemberId");

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentResponses_BoardMembers_BoardMemberId",
                table: "AssessmentResponses",
                column: "BoardMemberId",
                principalTable: "BoardMembers",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AssessmentResponses_BoardMembers_BoardMemberId",
                table: "AssessmentResponses");

            migrationBuilder.RenameColumn(
                name: "BoardMemberId",
                table: "AssessmentResponses",
                newName: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_AssessmentResponses_Users_UserId",
                table: "AssessmentResponses",
                column: "UserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");
        }
    }
}
