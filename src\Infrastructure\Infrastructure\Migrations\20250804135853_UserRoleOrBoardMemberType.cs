﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UserRoleOrBoardMemberType : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "UserRoleOrBoardMemberType",
                table: "ResolutionMemberVoteComments",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AlterColumn<int>(
                name: "VoteResult",
                table: "ResolutionItemVotes",
                type: "int",
                nullable: false,
                comment: "Vote result for this specific resolution item (NotVotedYet=0, Accept=1, Reject=2)",
                oldClrType: typeof(int),
                oldType: "int",
                oldDefaultValue: 1,
                oldComment: "Vote result for this specific resolution item (NotVotedYet=0, Accept=1, Reject=2)");

            migrationBuilder.AddColumn<string>(
                name: "UserRoleOrBoardMemberType",
                table: "ResolutionItemVoteComments",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "UserRoleOrBoardMemberType",
                table: "ResolutionMemberVoteComments");

            migrationBuilder.DropColumn(
                name: "UserRoleOrBoardMemberType",
                table: "ResolutionItemVoteComments");

            migrationBuilder.AlterColumn<int>(
                name: "VoteResult",
                table: "ResolutionItemVotes",
                type: "int",
                nullable: false,
                defaultValue: 1,
                comment: "Vote result for this specific resolution item (NotVotedYet=0, Accept=1, Reject=2)",
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "Vote result for this specific resolution item (NotVotedYet=0, Accept=1, Reject=2)");
        }
    }
}
