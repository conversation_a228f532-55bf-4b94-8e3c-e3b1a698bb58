﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UserRoleOrBoardMemberTypeUpdate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "UserRoleOrBoardMemberType",
                table: "ResolutionMemberVoteComments",
                type: "int",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddColumn<bool>(
                name: "IsBoardMemberComment",
                table: "ResolutionMemberVoteComments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AlterColumn<int>(
                name: "UserRoleOrBoardMemberType",
                table: "ResolutionItemVoteComments",
                type: "int",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddColumn<bool>(
                name: "IsBoardMemberComment",
                table: "ResolutionItemVoteComments",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsBoardMemberComment",
                table: "ResolutionMemberVoteComments");

            migrationBuilder.DropColumn(
                name: "IsBoardMemberComment",
                table: "ResolutionItemVoteComments");

            migrationBuilder.AlterColumn<string>(
                name: "UserRoleOrBoardMemberType",
                table: "ResolutionMemberVoteComments",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<string>(
                name: "UserRoleOrBoardMemberType",
                table: "ResolutionItemVoteComments",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");
        }
    }
}
