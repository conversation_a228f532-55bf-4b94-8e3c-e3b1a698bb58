using Abstraction.Contract.Service;
using Abstraction.Contracts.Repository.AssessmentManagement;
using Domain.Entities.AssessmentManagement;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repository.AssessmentManagement
{
    /// <summary>
    /// Repository implementation for Answer entity operations
    /// Provides data access functionality using Entity Framework Core
    /// </summary>
    public class AnswerRepository : GenericRepository, IAnswerRepository
    {
        public AnswerRepository(AppDbContext repositoryContext, ICurrentUserService currentUserService)
            : base(repositoryContext, currentUserService)
        {
        }

        /// <summary>
        /// Gets all answers for a specific assessment response
        /// </summary>
        /// <param name="responseId">Assessment response identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Collection of answers for the specified response</returns>
        public async Task<IEnumerable<Answer>> GetAnswersByResponseIdAsync(int responseId, bool trackChanges = false)
        {
            return await GetByCondition<Answer>(
                a => a.ResponseId == responseId && (a.IsDeleted == false || a.IsDeleted == null),
                trackChanges)
                .ToListAsync();
        }

        /// <summary>
        /// Gets answers with their selected options for a specific response
        /// </summary>
        /// <param name="responseId">Assessment response identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Collection of answers with options for the specified response</returns>
        public async Task<IEnumerable<Answer>> GetAnswersWithOptionsByResponseIdAsync(int responseId, bool trackChanges = false)
        {
            return await GetByCondition<Answer>(
                a => a.ResponseId == responseId && (a.IsDeleted == false || a.IsDeleted == null),
                trackChanges)
                .Include(a => a.SelectedOptions)
                .ThenInclude(ao => ao.Option)
                .Include(a => a.Question)
                .ToListAsync();
        }

        /// <summary>
        /// Gets a specific answer by question and response
        /// </summary>
        /// <param name="questionId">Question identifier</param>
        /// <param name="responseId">Response identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Answer for the specified question and response</returns>
        public async Task<Answer?> GetAnswerByQuestionAndResponseAsync(int questionId, int responseId, bool trackChanges = false)
        {
            return await GetByCondition<Answer>(
                a => a.QuestionId == questionId && a.ResponseId == responseId && (a.IsDeleted == false || a.IsDeleted == null),
                trackChanges)
                .Include(a => a.SelectedOptions)
                .ThenInclude(ao => ao.Option)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Deletes multiple answers
        /// </summary>
        /// <param name="answers">Collection of answers to delete</param>
        /// <returns>Task representing the asynchronous operation</returns>
        public async Task DeleteRangeAsync(IEnumerable<Answer> answers)
        {
            try
            {
                var answersList = answers.ToList();
                if (answersList.Any())
                {
                    // Soft delete by setting IsDeleted flag
                    foreach (var answer in answersList)
                    {
                        answer.IsDeleted = true;
                        answer.DeletedAt = DateTime.Now;
                        answer.DeletedBy = _currentUserService.UserId;
                    }

                    RepositoryContext.Set<Answer>().UpdateRange(answersList);
                    await RepositoryContext.SaveChangesAsync(_currentUserService.UserId.GetValueOrDefault());
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error deleting answers from the database.", ex);
            }
        }

        /// <summary>
        /// Adds multiple answers
        /// </summary>
        /// <param name="answers">Collection of answers to add</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the asynchronous operation</returns>
        public async Task AddRangeAsync(IEnumerable<Answer> answers, CancellationToken cancellationToken = default)
        {
            try
            {
                var answersList = answers.ToList();
                if (answersList.Any())
                {
                    await RepositoryContext.Set<Answer>().AddRangeAsync(answersList, cancellationToken);
                    await RepositoryContext.SaveChangesAsync(_currentUserService.UserId.GetValueOrDefault());
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error adding answers to the database.", ex);
            }
        }
    }
}
