using Abstraction.Contract.Service;
using Abstraction.Contracts.Repository.AssessmentManagement;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Abstraction.Constants;
using Domain.Entities.DocumentManagement;

namespace Infrastructure.Repository.AssessmentManagement
{
    /// <summary>
    /// Repository implementation for Assessment entity operations
    /// Provides data access functionality using Entity Framework Core
    /// </summary>
    public class AssessmentResponseRepository : GenericRepository, IAssessmentResponseRepository
    {
        public AssessmentResponseRepository(AppDbContext repositoryContext, ICurrentUserService currentUserService)
            : base(repositoryContext, currentUserService)
        {
        }
        public async Task<AssessmentResponse> GetAssessmentResponseByUserAsync(int MemberId, int AssessmentId)
        {
            return await RepositoryContext.Set<AssessmentResponse>()
                .FirstOrDefaultAsync(r => r.BoardMemberId == MemberId && r.AssessmentId == AssessmentId);
        }

    }
}
