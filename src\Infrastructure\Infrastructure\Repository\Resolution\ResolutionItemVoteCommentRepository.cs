using Abstraction.Contract.Service;
using Abstraction.Contracts.Repository.Resolution;
using Application.Base.Abstracts;
using Domain.Entities.ResolutionManagement;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repository.Resolution
{
    /// <summary>
    /// Repository implementation for ResolutionItemVoteComment entity operations
    /// Inherits from GenericRepository and implements IResolutionItemVoteCommentRepository
    /// Provides specific methods for resolution item vote comment business logic
    /// </summary>
    public class ResolutionItemVoteCommentRepository : GenericRepository, IResolutionItemVoteCommentRepository
    {
        #region Constructor

        public ResolutionItemVoteCommentRepository(AppDbContext repositoryContext, ICurrentUserService currentUserService)
            : base(repositoryContext, currentUserService)
        {
        }

        #endregion

        #region Methods

        /// <summary>
        /// Retrieves all comments associated with a specific ResolutionItemVote
        /// Gets comments for the ResolutionItem that the ResolutionItemVote is voting on
        /// Includes CreatedByUser navigation property and filters out soft-deleted comments
        /// Orders comments by creation date (newest first)
        /// </summary>
        /// <param name="resolutionItemVoteId">The ID of the ResolutionItemVote to get comments for</param>
        /// <param name="trackChanges">Whether to track changes for the returned entities</param>
        /// <returns>List of ResolutionItemVoteComment entities with related user data</returns>
        public async Task<List<ResolutionItemVoteComment>> GetCommentsByResolutionItemVoteIdAsync(int resolutionItemVoteId, bool trackChanges)
        {
            // First get the ResolutionItemVote to get the ResolutionItemId
            var resolutionItemVote = await GetByCondition<ResolutionItemVote>(
                x => x.Id == resolutionItemVoteId,
                trackChanges: false)
                .FirstOrDefaultAsync();

            if (resolutionItemVote == null)
                return new List<ResolutionItemVoteComment>();

            // Get all comments for the ResolutionItem that this vote is for
            var query = GetByCondition<ResolutionItemVoteComment>(
                x => x.ResolutionItemID == resolutionItemVote.ResolutionItemId &&
                     (x.IsDeleted == null || x.IsDeleted == false),
                trackChanges);

            return await query
                .Include(c => c.CreatedByUser)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync();
        }

        #endregion
    }
}
