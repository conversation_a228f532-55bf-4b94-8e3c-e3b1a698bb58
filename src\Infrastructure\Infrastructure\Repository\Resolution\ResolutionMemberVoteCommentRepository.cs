using Abstraction.Contract.Service;
using Abstraction.Contracts.Repository.Resolution;
using Application.Base.Abstracts;
using Domain.Entities.ResolutionManagement;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repository.Resolution
{
    /// <summary>
    /// Repository implementation for Resolution entity operations
    /// Inherits from GenericRepository and implements IResolutionHistoryRepository
    /// Provides specific methods for resolution business logic
    /// </summary>
    public class ResolutionMemberVoteCommentRepository : GenericRepository, IResolutionMemberVoteCommentRepository
    {
        #region Constructor

        public ResolutionMemberVoteCommentRepository(AppDbContext repositoryContext, ICurrentUserService currentUserService)
            : base(repositoryContext, currentUserService)
        {
        }


        #endregion

    }
}
