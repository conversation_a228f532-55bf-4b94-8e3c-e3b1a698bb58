﻿using Infrastructure.Data;
using Abstraction.Contract.Service;
using Domain.Entities.ResolutionManagement;
using Microsoft.EntityFrameworkCore;
using Domain.Entities.FundManagement;
using Abstraction.Contract.Repository.Resolution;
using Resources;
using Domain.Entities.ResolutionManagement.Enums;
using System.Runtime.InteropServices;



namespace Infrastructure.Repository.Resolution
{
    public class ResolutionMemberVoteRepository : GenericRepository, IResolutionMemberVoteRepository
    {
        public ResolutionMemberVoteRepository(AppDbContext repositoryContext, ICurrentUserService currentUserService)
            : base(repositoryContext, currentUserService)
        {

        }

        public async Task<ResolutionMemberVote> GetByMemeberAndResolutionAsync(int resolutionId, int memberId, bool trackChanges)
        {
            var query = GetByCondition<ResolutionMemberVote>(
                x => x.ResolutionId == resolutionId && x.BoardMemberID == memberId,
                trackChanges);

            return await query
                .Include(r => r.ResolutionMemberVoteComments).ThenInclude(c => c.CreatedByUser)
                .Include(r => r.ResolutionItemVotes.OrderBy(ri => ri.ResolutionItem.DisplayOrder)).ThenInclude(r => r.ResolutionItem)
                .ThenInclude(c => c.ResolutionItemVoteComments).ThenInclude(r => r.CreatedByUser)
                .FirstOrDefaultAsync();
        }

        public async Task<ResolutionMemberVote> GetByIdIncludedItemVotesAsync(int id,  bool trackChanges)
        {
            var query =   GetByCondition<ResolutionMemberVote>(
                x => x.Id == id,
                trackChanges);

            return  await query
                .Include(r => r.ResolutionItemVotes.OrderBy(ri => ri.ResolutionItem.DisplayOrder)).ThenInclude(r => r.ResolutionItem)
                .FirstOrDefaultAsync();
        }

        public async Task<bool> MemberHasVoted(int resolutionId, int memberId, bool trackChanges)
        {
            var query = GetByCondition<ResolutionMemberVote>(
                x => x.ResolutionId == resolutionId && x.BoardMemberID == memberId,
                trackChanges);

            var vote = await query.FirstOrDefaultAsync();
            return vote.VoteResult != VoteResult.NotVotedYet && vote.VoteResult != VoteResult.NotEligibleToVote;
        }
        public async Task<List<ResolutionMemberVote>> GetMembersByResolutionIdAsync(int resolutionId, bool trackChanges)
        {
            var query = GetByCondition<ResolutionMemberVote>(x => x.ResolutionId == resolutionId,trackChanges);
            return await query
                .Include(r => r.BoardMember).ThenInclude(c => c.User).Include(c=>c.Resolution).Include(c=>c.ResolutionItemVotes).ToListAsync();    
        }

        public async Task<List<string>> GetMembersByResolutionItemIdAndVoteResultAsync(int resolutionItemId, ItemVoteResult itemVoteResult, bool trackChanges)
        {
            VoteResult memberVote = VoteResult.NotVotedYet;
            var resolutionId =  await GetByCondition<ResolutionItemVote>(x => x.ResolutionItemId == resolutionItemId, trackChanges)
                .Select(x => x.ResolutionMemberVote.ResolutionId).FirstOrDefaultAsync();
            switch (itemVoteResult)
            {
                case ItemVoteResult.Accepted:
                    memberVote = VoteResult.Accept;
                    break;
                case ItemVoteResult.Rejected:
                    memberVote = VoteResult.Reject;
                    break;
                case ItemVoteResult.VotingInProgress:
                    memberVote = VoteResult.NotVotedYet;
                    break;
            }
            var query = await GetByCondition<ResolutionMemberVote>(x => x.ResolutionId == resolutionId && x.ResolutionItemVotes.Any(ri => ri.ResolutionItemId == resolutionItemId && ri.VoteResult == memberVote), trackChanges)
                .Include(r => r.BoardMember).ThenInclude(c => c.User).ToListAsync();
 
            return query.Select(x => x.BoardMember.User.FullName).ToList();
        }

        public async Task<bool> ResolutionMemberVoteHasRevoteRequest(int resolutionId, int memberId, bool trackChanges)
        {
            var vote = await  GetByCondition<ResolutionMemberVote>(c=>c.ResolutionId == resolutionId && c.BoardMemberID == memberId, trackChanges).Include(c=>c.ResolutionMemberVoteStatusHistories).FirstOrDefaultAsync();
            return  vote.ResolutionMemberVoteStatusHistories.OrderByDescending(c=>c.CreatedAt).FirstOrDefault().StatusID == (int)ResolutionMemberVoteStatusEnum.RequestRevote;
            
        }

        public async Task<ResolutionMemberVote> GetMemberVoteIncludeResolutionAsync(int resolutionMemberVoteId,bool trackChanges)
        {
            var query = GetByCondition<ResolutionMemberVote>(
                x => x.Id == resolutionMemberVoteId,
                trackChanges);

            return await query
                .Include(r => r.Resolution)
                .FirstOrDefaultAsync();
        }
    }
}
