using Abstraction.Constants;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Repository.Resolution;
using Application.Base.Abstracts;
using Azure.Core;
using Domain.Entities.ResolutionManagement;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Dapper;
using System.Data;
using Microsoft.Data.SqlClient;
using Domain.Entities.FundManagement;
using Domain.Entities.Users;
using Domain.Entities.Shared;

namespace Infrastructure.Repository.Resolution
{
    /// <summary>
    /// Repository implementation for Resolution entity operations
    /// Inherits from GenericRepository and implements IResolutionRepository
    /// Provides specific methods for resolution business logic
    /// </summary>
    public class ResolutionRepository : GenericRepository, IResolutionRepository
    {
        #region Constructor

        public ResolutionRepository(AppDbContext repositoryContext, ICurrentUserService currentUserService)
            : base(repositoryContext, currentUserService)
        {
        }

        #endregion

        #region IResolutionRepository Implementation

        /// <summary>
        /// Gets all resolutions for a specific fund
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Collection of resolutions for the fund</returns>
        public IQueryable<Domain.Entities.ResolutionManagement.Resolution> GetResolutionsByFundIdAsync(int fundId, bool trackChanges = false)
        {
            var query = GetByCondition<Domain.Entities.ResolutionManagement.Resolution>(
                r => r.FundId == fundId,
                trackChanges);

            return query
                .Include(r => r.Fund)
                .Include(r => r.ResolutionType)
                .Include(r => r.Attachment)
                .OrderByDescending(r => r.UpdatedAt ?? r.CreatedAt);

        }

        /// <summary>
        /// Gets resolutions by status for a specific fund
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <param name="status">Resolution status</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Collection of resolutions with specified status</returns>
        public async Task<IEnumerable<Domain.Entities.ResolutionManagement.Resolution>> GetResolutionsByStatusAsync(int fundId, ResolutionStatusEnum status, bool trackChanges = false)
        {
            var query = GetByCondition<Domain.Entities.ResolutionManagement.Resolution>(
                r => r.FundId == fundId && r.Status == status,
                trackChanges);

            return await query
                .Include(r => r.Fund)
                .Include(r => r.ResolutionType)
                .Include(r => r.Attachment)
                .OrderByDescending(r => r.UpdatedAt ?? r.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// Gets a resolution by its code
        /// </summary>
        /// <param name="code">Resolution code</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Resolution with the specified code or null</returns>
        public async Task<Domain.Entities.ResolutionManagement.Resolution?> GetResolutionByCodeAsync(string code, bool trackChanges = false)
        {
            var query = GetByCondition<Domain.Entities.ResolutionManagement.Resolution>(
                r => r.Code == code,
                trackChanges);

            return await query
                .Include(r => r.Fund)
                .Include(r => r.ResolutionType)
                .Include(r => r.Attachment)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Gets resolutions for a fund in a specific year
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <param name="year">Year to filter by</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Collection of resolutions for the specified year</returns>
        public async Task<IEnumerable<Domain.Entities.ResolutionManagement.Resolution>> GetResolutionsByYearAsync(int fundId, int year, bool trackChanges = false)
        {
            var query = GetByCondition<Domain.Entities.ResolutionManagement.Resolution>(
                r => r.FundId == fundId && r.ResolutionDate.Year == year,
                trackChanges);

            return await query
                .Include(r => r.Fund)
                .Include(r => r.ResolutionType)
                .Include(r => r.Attachment)
                .OrderByDescending(r => r.ResolutionDate)
                .ToListAsync();
        }

        /// <summary>
        /// Gets the highest sequential number for resolutions in a specific fund and year
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <param name="year">Year to check</param>
        /// <returns>Highest sequential number used</returns>
        public async Task<int> GetMaxSequentialNumberAsync(int fundId, int year)
        {
            var resolutions = await GetByCondition<Domain.Entities.ResolutionManagement.Resolution>(
                r => r.FundId == fundId && r.ResolutionDate.Year == year,
                trackChanges: false)
                .Select(r => r.Code)
                .ToListAsync();

            if (!resolutions.Any())
                return 0;

            // Extract sequential numbers from codes (format: fundcode/year/number)
            var maxNumber = 0;
            foreach (var code in resolutions)
            {
                var parts = code.Split('/');
                if (parts.Length == 3 && int.TryParse(parts[2], out var number))
                {
                    maxNumber = Math.Max(maxNumber, number);
                }
            }

            return maxNumber;
        }

        /// <summary>
        /// Gets resolution with its items included
        /// </summary>
        /// <param name="resolutionId">Resolution identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Resolution with items or null</returns>
        public async Task<Domain.Entities.ResolutionManagement.Resolution?> GetResolutionWithItemsAsync(int resolutionId, bool trackChanges = false)
        {
            var query = GetByCondition<Domain.Entities.ResolutionManagement.Resolution>(
                r => r.Id == resolutionId,
                trackChanges);

            return await query
                .Include(r => r.Fund)
                .Include(r => r.ResolutionType)
                .Include(r => r.Attachment)
                .Include(r => r.ResolutionItems.OrderBy(ri => ri.DisplayOrder))
                .ThenInclude(ri => ri.ConflictMembers)
                .ThenInclude(cm => cm.BoardMember)
                .ThenInclude(bm => bm.User)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Gets resolution with its items included
        /// </summary>
        /// <param name="resolutionId">Resolution identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Resolution with items or null</returns>
        public async Task<Domain.Entities.ResolutionManagement.Resolution?> GetResolutionWithItemsAndConflictAsync(int resolutionId, bool trackChanges = false)
        {
            var query = GetByCondition<Domain.Entities.ResolutionManagement.Resolution>(
                r => r.Id == resolutionId,
                trackChanges);

            return await query
                .Include(r => r.ResolutionItems.OrderBy(ri => ri.DisplayOrder))
                .ThenInclude(ri => ri.ConflictMembers)
                .ThenInclude(cm => cm.BoardMember)
                .ThenInclude(bm => bm.User)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Checks if a resolution code already exists
        /// </summary>
        /// <param name="code">Resolution code to check</param>
        /// <returns>True if code exists, false otherwise</returns>
        public async Task<bool> ResolutionCodeExistsAsync(string code)
        {
            return await GetByCondition<Domain.Entities.ResolutionManagement.Resolution>(
                r => r.Code == code,
                trackChanges: false)
                .AnyAsync();
        }

        /// <summary>
        /// Gets resolution with all related data (items, attachments, history)
        /// Uses Entity Framework when trackChanges=true for update operations
        /// Uses Dapper when trackChanges=false for read-only operations with better performance
        /// </summary>
        /// <param name="resolutionId">Resolution identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Resolution with all related data or null</returns>
        public async Task<Domain.Entities.ResolutionManagement.Resolution?> GetResolutionWithAllDataAsync(int resolutionId, bool trackChanges = false)
        {
            try
            {
                // Use Entity Framework when tracking is needed for update operations
                if (trackChanges)
                {
                    return await GetResolutionWithAllDataUsingEF(resolutionId, trackChanges: true);
                }

                // Use Dapper for read-only operations (better performance)
                return await GetResolutionWithAllDataUsingDapper(resolutionId);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// Gets resolution with all related data using Entity Framework with proper change tracking
        /// Used when trackChanges=true for update operations
        /// </summary>
        private async Task<Domain.Entities.ResolutionManagement.Resolution?> GetResolutionWithAllDataUsingEF(int resolutionId, bool trackChanges)
        {
            var query = GetByCondition<Domain.Entities.ResolutionManagement.Resolution>(
                           r => r.Id == resolutionId,
                           trackChanges);

            return await query
                .Include(r => r.Fund)
                .Include(r => r.ResolutionType)
                .Include(r => r.Attachment)
                .Include(r => r.OtherAttachments)
                .ThenInclude(oa => oa.Attachment)
                .Include(r => r.ResolutionItems.OrderBy(ri => ri.DisplayOrder))
                .ThenInclude(ri => ri.ConflictMembers)
                .ThenInclude(cm => cm.BoardMember)
                .ThenInclude(bm => bm.User)
                .Include(r => r.ResolutionMemberVotes)
                //.ThenInclude(v => v.BoardMember)
                //.ThenInclude(bm => bm.User)
                .Include(r => r.ResolutionStatusHistories)
                .ThenInclude(rsh => rsh.ResolutionStatus)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Gets resolution with all related data using Dapper for improved performance
        /// Used when trackChanges=false for read-only operations
        /// </summary>
        private async Task<Domain.Entities.ResolutionManagement.Resolution?> GetResolutionWithAllDataUsingDapper(int resolutionId)
        {
            try
            {
                using var connection = new SqlConnection(RepositoryContext.Database.GetConnectionString());
                var parameters = new { resolutionId };

                // Query 1: Main Resolution with Fund, ResolutionType, and main Attachment
                var sql1 = @"
                    SELECT r.*, f.*, rt.*, a.*
                    FROM Resolutions r
                    LEFT JOIN Funds f ON r.FundId = f.Id
                    LEFT JOIN ResolutionTypes rt ON r.ResolutionTypeId = rt.Id
                    LEFT JOIN Attachments a ON r.AttachmentId = a.Id
                    WHERE r.Id = @resolutionId AND (r.IsDeleted = 0 OR r.IsDeleted IS NULL)";

                var resolutionData = await connection.QueryAsync<Domain.Entities.ResolutionManagement.Resolution, Domain.Entities.FundManagement.Fund, ResolutionType, Attachment, Domain.Entities.ResolutionManagement.Resolution>(
                    sql1,
                    (resolution, fund, resolutionType, attachment) =>
                    {
                        if (resolution != null)
                        {
                            resolution.Fund = fund;
                            resolution.ResolutionType = resolutionType;
                            resolution.Attachment = attachment;
                        }
                        return resolution;
                    },
                    parameters,
                    splitOn: "Id,Id,Id"
                );

                var mainResolution = resolutionData.FirstOrDefault();
                if (mainResolution == null)
                    return null;

                // Initialize collections
                mainResolution.ResolutionItems = new List<ResolutionItem>();
                mainResolution.ResolutionMemberVotes = new List<ResolutionMemberVote>();
                mainResolution.ResolutionStatusHistories = new List<ResolutionStatusHistory>();
                mainResolution.OtherAttachments = new List<ResolutionAttachment>();

                // Query 2: ResolutionItems with ConflictMembers, BoardMembers, and Users
                var sql2 = @"
                    SELECT ri.*, cm.*, bm.*, u.*
                    FROM ResolutionItems ri
                    LEFT JOIN ResolutionItemConflicts cm ON ri.Id = cm.ResolutionItemId
                    LEFT JOIN BoardMembers bm ON cm.BoardMemberId = bm.Id
                    LEFT JOIN AspNetUsers u ON bm.UserId = u.Id
                    WHERE ri.ResolutionId = @resolutionId AND (ri.IsDeleted = 0 OR ri.IsDeleted IS NULL)
                    ORDER BY ri.DisplayOrder";

                var resolutionItemsData = await connection.QueryAsync<ResolutionItem, ResolutionItemConflict, BoardMember, User, ResolutionItem>(
                    sql2,
                    (item, conflict, boardMember, user) =>
                    {
                        if (item != null)
                        {
                            item.ConflictMembers ??= new List<ResolutionItemConflict>();
                            item.ResolutionItemVotes ??= new List<ResolutionItemVote>();
                            item.ResolutionItemVoteComments ??= new List<ResolutionItemVoteComment>();

                            if (conflict != null)
                            {
                                conflict.BoardMember = boardMember;
                                if (boardMember != null)
                                    boardMember.User = user;
                                item.ConflictMembers.Add(conflict);
                            }
                        }
                        return item;
                    },
                    parameters,
                    splitOn: "Id,Id,Id"
                );

                // Group ResolutionItems by Id to handle duplicates from JOINs
                var itemsDict = new Dictionary<int, ResolutionItem>();
                foreach (var item in resolutionItemsData.Where(x => x != null))
                {
                    if (!itemsDict.ContainsKey(item.Id))
                    {
                        item.ConflictMembers ??= new List<ResolutionItemConflict>();
                        item.ResolutionItemVotes ??= new List<ResolutionItemVote>();
                        item.ResolutionItemVoteComments ??= new List<ResolutionItemVoteComment>();
                        itemsDict[item.Id] = item;
                    }
                    else
                    {
                        // Merge conflict members from duplicate rows
                        var existingItem = itemsDict[item.Id];
                        foreach (var conflict in item.ConflictMembers ?? new List<ResolutionItemConflict>())
                        {
                            if (conflict != null && !existingItem.ConflictMembers.Any(c => c.Id == conflict.Id))
                            {
                                existingItem.ConflictMembers.Add(conflict);
                            }
                        }
                    }
                }

                mainResolution.ResolutionItems = itemsDict.Values.OrderBy(x => x.DisplayOrder).ToList();

                // Query 3: ResolutionMemberVotes with BoardMembers and Users
                var sql3 = @"
                    SELECT rmv.*, bm.*, u.*
                    FROM ResolutionMemberVotes rmv
                    LEFT JOIN BoardMembers bm ON rmv.BoardMemberID = bm.Id
                    LEFT JOIN AspNetUsers u ON bm.UserId = u.Id
                    WHERE rmv.ResolutionId = @resolutionId AND (rmv.IsDeleted = 0 OR rmv.IsDeleted IS NULL)";

                var memberVotesData = await connection.QueryAsync<ResolutionMemberVote, BoardMember, User, ResolutionMemberVote>(
                    sql3,
                    (vote, boardMember, user) =>
                    {
                        if (vote != null)
                        {
                            vote.BoardMember = boardMember;
                            if (boardMember != null)
                                boardMember.User = user;
                            vote.ResolutionMemberVoteComments ??= new List<ResolutionMemberVoteComment>();
                            vote.ResolutionItemVotes ??= new List<ResolutionItemVote>();
                        }
                        return vote;
                    },
                    parameters,
                    splitOn: "Id,Id"
                );

                mainResolution.ResolutionMemberVotes = memberVotesData.Where(x => x != null).ToList();

                // Query 4: ResolutionStatusHistories with ResolutionStatus
                var sql4 = @"
                    SELECT rsh.*, rs.*
                    FROM ResolutionStatusHistories rsh
                    LEFT JOIN ResolutionStatus rs ON rsh.ResolutionStatusId = rs.Id
                    WHERE rsh.ResolutionId = @resolutionId AND (rsh.IsDeleted = 0 OR rsh.IsDeleted IS NULL)
                    ORDER BY rsh.CreatedAt";

                var statusHistoriesData = await connection.QueryAsync<ResolutionStatusHistory, ResolutionStatus, ResolutionStatusHistory>(
                    sql4,
                    (history, status) =>
                    {
                        if (history != null)
                            history.ResolutionStatus = status;
                        return history;
                    },
                    parameters,
                    splitOn: "Id"
                );

                mainResolution.ResolutionStatusHistories = statusHistoriesData.Where(x => x != null).ToList();

                // Query 5: OtherAttachments with Attachment details
                var sql5 = @"
                    SELECT oa.*, a.*
                    FROM ResolutionAttachments oa
                    LEFT JOIN Attachments a ON oa.AttachmentId = a.Id
                    WHERE oa.ResolutionId = @resolutionId AND (oa.IsDeleted = 0 OR oa.IsDeleted IS NULL)";

                var otherAttachmentsData = await connection.QueryAsync<ResolutionAttachment, Attachment, ResolutionAttachment>(
                    sql5,
                    (resolutionAttachment, attachment) =>
                    {
                        if (resolutionAttachment != null)
                            resolutionAttachment.Attachment = attachment;
                        return resolutionAttachment;
                    },
                    parameters,
                    splitOn: "Id"
                );

                mainResolution.OtherAttachments = otherAttachmentsData.Where(x => x != null).ToList();

                // Query 6: ResolutionItemVotes
                var sql6 = @"
                    SELECT riv.*
                    FROM ResolutionItemVotes riv
                    INNER JOIN ResolutionItems ri ON riv.ResolutionItemId = ri.Id
                    WHERE ri.ResolutionId = @resolutionId AND (riv.IsDeleted = 0 OR riv.IsDeleted IS NULL)";

                var itemVotesList = (await connection.QueryAsync<ResolutionItemVote>(sql6, parameters)).ToList();

                // Assign ResolutionItemVotes to their respective ResolutionItems
                foreach (var item in mainResolution.ResolutionItems)
                {
                    item.ResolutionItemVotes = itemVotesList.Where(v => v.ResolutionItemId == item.Id).ToList();
                }

                // Query 7: ResolutionMemberVoteComments with CreatedByUser
                var sql7 = @"
                    SELECT rmvc.*, u.*
                    FROM ResolutionMemberVoteComments rmvc
                    INNER JOIN ResolutionMemberVotes rmv ON rmvc.ResolutionMemberVoteID = rmv.Id
                    LEFT JOIN AspNetUsers u ON rmvc.CreatedBy = u.Id
                    WHERE rmv.ResolutionId = @resolutionId AND (rmvc.IsDeleted = 0 OR rmvc.IsDeleted IS NULL) order by rmvc.CreatedAt desc";

                var memberVoteCommentsData = await connection.QueryAsync<ResolutionMemberVoteComment, User, ResolutionMemberVoteComment>(
                    sql7,
                    (comment, user) =>
                    {
                        if (comment != null)
                            comment.CreatedByUser = user;
                        return comment;
                    },
                    parameters,
                    splitOn: "Id"
                );

                var memberVoteCommentsList = memberVoteCommentsData.Where(x => x != null).ToList();

                // Assign ResolutionMemberVoteComments to their respective ResolutionMemberVotes
                foreach (var vote in mainResolution.ResolutionMemberVotes)
                {
                    vote.ResolutionMemberVoteComments = memberVoteCommentsList.Where(c => c.ResolutionMemberVoteID == vote.Id).ToList();
                }

                // Query 8: ResolutionItemVoteComments with CreatedByUser
                var sql8 = @"
                    SELECT rivc.*, u.*
                    FROM ResolutionItemVoteComments rivc
                    INNER JOIN ResolutionItems ri ON rivc.ResolutionItemID = ri.Id
                    LEFT JOIN AspNetUsers u ON rivc.CreatedBy = u.Id
                    WHERE ri.ResolutionId = @resolutionId AND (rivc.IsDeleted = 0 OR rivc.IsDeleted IS NULL) order by rivc.CreatedAt desc";

                var itemVoteCommentsData = await connection.QueryAsync<ResolutionItemVoteComment, User, ResolutionItemVoteComment>(
                    sql8,
                    (comment, user) =>
                    {
                        if (comment != null)
                            comment.CreatedByUser = user;
                        return comment;
                    },
                    parameters,
                    splitOn: "Id"
                );

                var itemVoteCommentsList = itemVoteCommentsData.Where(x => x != null).ToList();

                // Assign ResolutionItemVoteComments to their respective ResolutionItems
                foreach (var item in mainResolution.ResolutionItems)
                {
                    item.ResolutionItemVoteComments = itemVoteCommentsList.Where(c => c.ResolutionItemID == item.Id).ToList();
                }

                return mainResolution;

            }
            catch (Exception ex)
            {

                throw;
            }
        }



        /// <summary>
        /// Gets resolution with all related data for voting (items, attachments, history) using Dapper for improved performance
        /// Filters ResolutionMemberVotes to only include votes for the specified member
        /// </summary>
        /// <param name="resolutionId">Resolution identifier</param>
        /// <param name="memberId">Board member identifier to filter votes</param>
        /// <param name="trackChanges">Whether to track changes for updates (ignored in Dapper implementation)</param>
        /// <returns>Resolution with all related voting data or null</returns>
        public async Task<Domain.Entities.ResolutionManagement.Resolution?> GetResolutionVote(int resolutionId, int memberId, bool trackChanges = false)
        {
            try
            {
                //var query = GetByCondition<Domain.Entities.ResolutionManagement.Resolution>(
                //r => r.Id == resolutionId,
                //trackChanges);

                //return await query
                //    .Include(r => r.Fund)
                //    .Include(r => r.ResolutionType)
                //    .Include(r => r.Attachment)
                //    .Include(r => r.OtherAttachments)
                //    .ThenInclude(oa => oa.Attachment)
                //    .Include(r => r.ResolutionMemberVotes.Where(v => v.BoardMemberID == memberId))
                //    .ThenInclude(rmv => rmv.BoardMember)
                //    .ThenInclude(bm => bm.User)
                //    .Include(r => r.ResolutionMemberVotes.Where(v => v.BoardMemberID == memberId))
                //    .ThenInclude(r => r.ResolutionMemberVoteComments).ThenInclude(c => c.CreatedByUser)
                //    .Include(r => r.ResolutionMemberVotes.Where(v => v.BoardMemberID == memberId))// Include only votes for the specific member
                //    .ThenInclude(r => r.ResolutionItemVotes.OrderBy(ri => ri.ResolutionItem.DisplayOrder))
                //    .ThenInclude(r => r.ResolutionItem)
                //    .ThenInclude(c => c.ResolutionItemVoteComments).ThenInclude(r => r.CreatedByUser)
                //    .Include(r => r.ResolutionStatusHistories)
                //    .ThenInclude(rsh => rsh.ResolutionStatus).FirstOrDefaultAsync();

                using var connection = new SqlConnection(RepositoryContext.Database.GetConnectionString());
                var parameters = new { resolutionId, memberId };

                // Query 1: Main Resolution with Fund, ResolutionType, and main Attachment
                var sql1 = @"
                    SELECT r.*, f.*, rt.*, a.*
                    FROM Resolutions r
                    LEFT JOIN Funds f ON r.FundId = f.Id
                    LEFT JOIN ResolutionTypes rt ON r.ResolutionTypeId = rt.Id
                    LEFT JOIN Attachments a ON r.AttachmentId = a.Id
                    WHERE r.Id = @resolutionId AND (r.IsDeleted = 0 OR r.IsDeleted IS NULL)";

                var resolutionData = await connection.QueryAsync<Domain.Entities.ResolutionManagement.Resolution, Domain.Entities.FundManagement.Fund, ResolutionType, Attachment, Domain.Entities.ResolutionManagement.Resolution>(
                    sql1,
                    (resolution, fund, resolutionType, attachment) =>
                    {
                        if (resolution != null)
                        {
                            resolution.Fund = fund;
                            resolution.ResolutionType = resolutionType;
                            resolution.Attachment = attachment;
                        }
                        return resolution;
                    },
                    parameters,
                    splitOn: "Id,Id,Id"
                );

                var mainResolution = resolutionData.FirstOrDefault();
                if (mainResolution == null)
                    return null;

                // Initialize collections
                mainResolution.ResolutionItems = new List<ResolutionItem>();
                mainResolution.ResolutionMemberVotes = new List<ResolutionMemberVote>();
                mainResolution.ResolutionStatusHistories = new List<ResolutionStatusHistory>();
                mainResolution.OtherAttachments = new List<ResolutionAttachment>();

                // Query 2: OtherAttachments with Attachment details
                var sql2 = @"
                    SELECT oa.*, a.*
                    FROM ResolutionAttachments oa
                    LEFT JOIN Attachments a ON oa.AttachmentId = a.Id
                    WHERE oa.ResolutionId = @resolutionId AND (oa.IsDeleted = 0 OR oa.IsDeleted IS NULL)";

                var otherAttachmentsData = await connection.QueryAsync<ResolutionAttachment, Attachment, ResolutionAttachment>(
                    sql2,
                    (resolutionAttachment, attachment) =>
                    {
                        if (resolutionAttachment != null)
                            resolutionAttachment.Attachment = attachment;
                        return resolutionAttachment;
                    },
                    parameters,
                    splitOn: "Id"
                );

                mainResolution.OtherAttachments = otherAttachmentsData.Where(x => x != null).ToList();

                // Query 3: ResolutionMemberVotes filtered by memberId
                var sql3 = @"
                    SELECT rmv.*
                    FROM ResolutionMemberVotes rmv
                    WHERE rmv.ResolutionId = @resolutionId
                    AND rmv.BoardMemberID = @memberId
                    AND (rmv.IsDeleted = 0 OR rmv.IsDeleted IS NULL)";

                var memberVotes = (await connection.QueryAsync<ResolutionMemberVote>(sql3, parameters)).ToList();

                // Initialize collections for member votes
                foreach (var vote in memberVotes)
                {
                    vote.ResolutionMemberVoteComments ??= new List<ResolutionMemberVoteComment>();
                    vote.ResolutionItemVotes ??= new List<ResolutionItemVote>();
                }

                mainResolution.ResolutionMemberVotes = memberVotes;
                foreach (var item in mainResolution.ResolutionMemberVotes)
                {
                    item.Resolution =   resolutionData.FirstOrDefault();
                }
                // Query 4: ResolutionMemberVoteComments with CreatedByUser for the specific member
                var sql4 = @"
                    SELECT rmvc.*, u.*
                    FROM ResolutionMemberVoteComments rmvc
                    INNER JOIN ResolutionMemberVotes rmv ON rmvc.ResolutionMemberVoteID = rmv.Id
                    LEFT JOIN AspNetUsers u ON rmvc.CreatedBy = u.Id
                    WHERE rmv.ResolutionId = @resolutionId
                    AND rmv.BoardMemberID = @memberId
                    AND (rmvc.IsDeleted = 0 OR rmvc.IsDeleted IS NULL) order by rmvc.CreatedAt desc";

                var memberVoteCommentsData = await connection.QueryAsync<ResolutionMemberVoteComment, User, ResolutionMemberVoteComment>(
                    sql4,
                    (comment, user) =>
                    {
                        if (comment != null)
                            comment.CreatedByUser = user;
                        return comment;
                    },
                    parameters,
                    splitOn: "Id"
                );

                var memberVoteCommentsList = memberVoteCommentsData.Where(x => x != null).ToList();

                // Assign ResolutionMemberVoteComments to their respective ResolutionMemberVotes
                foreach (var vote in mainResolution.ResolutionMemberVotes)
                {
                    vote.ResolutionMemberVoteComments = memberVoteCommentsList.Where(c => c.ResolutionMemberVoteID == vote.Id).ToList();
                }

                // Query 5: ResolutionItemVotes with ResolutionItem details for the specific member, ordered by DisplayOrder
                var sql5 = @"
                    SELECT riv.*, ri.*
                    FROM ResolutionItemVotes riv
                    INNER JOIN ResolutionMemberVotes rmv ON riv.ResolutionMemberVoteId = rmv.Id
                    INNER JOIN ResolutionItems ri ON riv.ResolutionItemId = ri.Id
                    WHERE rmv.ResolutionId = @resolutionId
                    AND rmv.BoardMemberID = @memberId
                    AND (riv.IsDeleted = 0 OR riv.IsDeleted IS NULL)
                    ORDER BY ri.DisplayOrder";

                var itemVotesData = await connection.QueryAsync<ResolutionItemVote, ResolutionItem, ResolutionItemVote>(
                    sql5,
                    (itemVote, resolutionItem) =>
                    {
                        if (itemVote != null)
                        {
                            itemVote.ResolutionItem = resolutionItem;
                            if (resolutionItem != null)
                            {
                                resolutionItem.ResolutionItemVoteComments ??= new List<ResolutionItemVoteComment>();
                            }
                        }
                        return itemVote;
                    },
                    parameters,
                    splitOn: "Id"
                );

                var itemVotesList = itemVotesData.Where(x => x != null).ToList();

                // Assign ResolutionItemVotes to their respective ResolutionMemberVotes
                foreach (var vote in mainResolution.ResolutionMemberVotes)
                {
                    vote.ResolutionItemVotes = itemVotesList.Where(iv => iv.ResolutionMemberVoteId == vote.Id).ToList();
                    
                }

                // Collect unique ResolutionItems from the item votes for the ResolutionItems collection
                var uniqueItems = itemVotesList
                    .Where(iv => iv.ResolutionItem != null)
                    .Select(iv => iv.ResolutionItem!)
                    .GroupBy(ri => ri.Id)
                    .Select(g => g.First())
                    .OrderBy(ri => ri.DisplayOrder)
                    .ToList();

                mainResolution.ResolutionItems = uniqueItems;

                // Query 6: ResolutionItemVoteComments with CreatedByUser for the specific member's item votes
                var sql6 = @"
                    SELECT rivc.*, u.*
                    FROM ResolutionItemVoteComments rivc
                    INNER JOIN ResolutionItems ri ON rivc.ResolutionItemID = ri.Id
                    LEFT JOIN AspNetUsers u ON rivc.CreatedBy = u.Id
                    WHERE ri.Id IN (
                        SELECT DISTINCT riv.ResolutionItemId
                        FROM ResolutionItemVotes riv
                        INNER JOIN ResolutionMemberVotes rmv ON riv.ResolutionMemberVoteId = rmv.Id
                        WHERE rmv.ResolutionId = @resolutionId AND rmv.BoardMemberID = @memberId
                    )
                    AND (rivc.IsDeleted = 0 OR rivc.IsDeleted IS NULL) order by rivc.CreatedAt desc";

                var itemVoteCommentsData = await connection.QueryAsync<ResolutionItemVoteComment, User, ResolutionItemVoteComment>(
                    sql6,
                    (comment, user) =>
                    {
                        if (comment != null)
                            comment.CreatedByUser = user;
                        return comment;
                    },
                    parameters,
                    splitOn: "Id"
                );

                var itemVoteCommentsList = itemVoteCommentsData.Where(x => x != null).ToList();

                // Assign ResolutionItemVoteComments to their respective ResolutionItems
                foreach (var item in mainResolution.ResolutionItems)
                {
                    item.ResolutionItemVoteComments = itemVoteCommentsList.Where(c => c.ResolutionItemID == item.Id).ToList();
                }

                // Query 7: ResolutionStatusHistories with ResolutionStatus
                var sql7 = @"
                    SELECT rsh.*, rs.*
                    FROM ResolutionStatusHistories rsh
                    LEFT JOIN ResolutionStatus rs ON rsh.ResolutionStatusId = rs.Id
                    WHERE rsh.ResolutionId = @resolutionId AND (rsh.IsDeleted = 0 OR rsh.IsDeleted IS NULL)
                    ORDER BY rsh.CreatedAt";

                var statusHistoriesData = await connection.QueryAsync<ResolutionStatusHistory, ResolutionStatus, ResolutionStatusHistory>(
                    sql7,
                    (history, status) =>
                    {
                        if (history != null)
                            history.ResolutionStatus = status;
                        return history;
                    },
                    parameters,
                    splitOn: "Id"
                );

                mainResolution.ResolutionStatusHistories = statusHistoriesData.Where(x => x != null).ToList();

                return mainResolution;


            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// Gets resolution with minimal data needed for edit screen
        /// Includes resolution items and attachment details but excludes heavy display-only data
        /// Optimized for edit screen performance by loading only essential data
        /// </summary>
        /// <param name="resolutionId">Resolution identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Resolution with edit-specific data or null</returns>
        public async Task<Domain.Entities.ResolutionManagement.Resolution?> GetResolutionForEditAsync(int resolutionId, bool trackChanges = false)
        {
            var query = GetByCondition<Domain.Entities.ResolutionManagement.Resolution>(
                r => r.Id == resolutionId,
                trackChanges);

            return await query
                .Include(r=>r.Fund)
                .Include(r=>r.ResolutionStatusHistories)
                .ThenInclude(r=>r.ResolutionStatus)
                .Include(r => r.Attachment) // Include main attachment details
                .Include(r => r.ResolutionItems.OrderBy(ri => ri.DisplayOrder))
                .ThenInclude(ri => ri.ConflictMembers)
                .ThenInclude(cm => cm.BoardMember)
                .ThenInclude(bm => bm.User)
                .Include(r => r.OtherAttachments)
                .ThenInclude(oa => oa.Attachment) // Include other attachment details
                .Include(r=>r.ParentResolution)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Gets child resolutions for a parent resolution
        /// </summary>
        /// <param name="parentResolutionId">Parent resolution identifier</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Collection of child resolutions</returns>
        public async Task<IEnumerable<Domain.Entities.ResolutionManagement.Resolution>> GetChildResolutionsAsync(int parentResolutionId, bool trackChanges = false)
        {
            var query = GetByCondition<Domain.Entities.ResolutionManagement.Resolution>(
                r => r.ParentResolutionId == parentResolutionId,
                trackChanges);

            return await query
                .Include(r => r.Fund)
                .Include(r => r.ResolutionType)
                .Include(r => r.Attachment)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// Gets resolutions by role-based filtering
        /// </summary>
        /// <param name="fundId">Fund identifier</param>
        /// <param name="userRole">User role for filtering</param>
        /// <param name="trackChanges">Whether to track changes for updates</param>
        /// <returns>Collection of resolutions filtered by role</returns>
        public IQueryable<Domain.Entities.ResolutionManagement.Resolution> GetResolutionsByRoleAsync //(int fundId, string userRole, bool trackChanges = false) //
                                                                                                     (int fundId, string? searchTerm, int? resolutionTypeId, ResolutionStatusEnum? status, DateTime? fromDate, DateTime? toDate, string userRole, bool trackChanges = false)
        {
            var query = GetByCondition<Domain.Entities.ResolutionManagement.Resolution>(
                r => r.FundId == fundId && !r.IsDeleted.Value
                && (string.IsNullOrWhiteSpace(searchTerm) || r.Code.Contains(searchTerm))
                && (!resolutionTypeId.HasValue || r.ResolutionTypeId == resolutionTypeId)
                && (status == null || r.Status == status)
                && (fromDate == null || r.ResolutionDate.Date >= fromDate.Value.Date)
                && (toDate == null || r.ResolutionDate.Date <= toDate.Value.Date)
                ,
                trackChanges);
            //var query = GetByCondition<Domain.Entities.ResolutionManagement.Resolution>(
            //    r => r.FundId == fundId && !r.IsDeleted.Value,
            //    trackChanges);
            // Apply role-based filtering
            switch (userRole.ToLower())
            {
                case RoleHelper.FundManager:
                    // Fund managers can see all resolutions
                    break;
                case RoleHelper.LegalCouncil:
                case RoleHelper.BoardSecretary:
                    // Legal council and board secretary can see pending, confirmed, rejected, and voting resolutions
                    query = query.Where(r => r.Status != ResolutionStatusEnum.Draft);
                    break;
                case RoleHelper.BoardMember:
                    // Board members can only see resolutions in voting or completed status
                    query = query.Where(r => r.Status == ResolutionStatusEnum.VotingInProgress ||
                                           r.Status == ResolutionStatusEnum.Approved ||
                                           r.Status == ResolutionStatusEnum.NotApproved);
                    break;
                default:
                    // Unknown role, return empty collection
                    return Enumerable.Empty<Domain.Entities.ResolutionManagement.Resolution>().AsQueryable();
            }

            return query
               // .Include(r => r.Fund)
               // .Include(r => r.ResolutionType)
               //.Include(r => r.Attachment)
               .Include(r => r.ResolutionStatusHistories.OrderByDescending(x => x.CreatedAt).Take(1))
                .ThenInclude(rsh => rsh.ResolutionStatus);

        }

        public async Task<int> GetFundIdByResolutionIdAsync(int resolutionId, bool trackChanges = false)
        {
            var query = await GetByIdAsync<Domain.Entities.ResolutionManagement.Resolution>(resolutionId,false);
            return query.FundId;
        }
        public async Task<int> GetBoardMemberIdByResolutionIdAndUserIdAsync(int resolutionId,int userId, bool trackChanges = false)
        {
            var query =      GetByCondition<Domain.Entities.ResolutionManagement.Resolution>(c => c.Id.Equals(resolutionId) && c.Fund.BoardMembers.Any(c => c.UserId.Equals(userId)), false).Include(c => c.Fund).ThenInclude(c => c.BoardMembers).Select(c => c.Fund.BoardMembers.Where(c => c.UserId.Equals(userId)).FirstOrDefault().Id).FirstOrDefaultAsync(); 
            return   await query;
        }
        #endregion
    }
}
