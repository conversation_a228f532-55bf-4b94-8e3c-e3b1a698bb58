using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Presentation.Bases;
using Abstraction.Base.Response;
using Abstraction.Constants;
using Application.Features.AssessmentManagement.Commands.CreateAssessment;

using Application.Features.AssessmentManagement.Dtos;
using Application.Features.AssessmentManagement.Queries.List;
using Application.Features.AssessmentManagement.Queries.GetById;
using Application.Features.AssessmentManagement.Queries.GetAssessmentDetails;
using Application.Features.AssessmentManagement.Commands.DeleteAssessment;
using Abstraction.Common.Wappers;
using Application.Features.Resolutions.Dtos;
using Application.Features.AssessmentManagement.Commands.UpdateAssessment;
using Application.Features.AssessmentManagement.Commands.RejectAssessment;
using Application.Features.AssessmentManagement.Commands.ApproveAssessment;
using Application.Features.AssessmentManagement.Commands.DistributeAssessment;
using Application.Features.AssessmentManagement.Commands.SubmitAssessmentResponse;
using Application.Features.AssessmentManagement.Queries.GetAssessmentResults;
using Application.Features.AssessmentManagement.Queries.GetFundPermission;

namespace Presentation.Controllers.Assessment
{
    /// <summary>
    /// API Controller for Assessment management operations
    /// Provides RESTful endpoints for assessment lifecycle management following CQRS pattern
    /// Supports Arabic/English localization and role-based authorization
    /// Based on requirements in AssessmentStories.md
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AssessmentController : AppControllerBase
    {
        #region CRUD Operations

        /// <summary>
        /// Get paginated list of assessments with optional filtering
        /// Accessible by Fund Manager, Legal Council, Board Secretary
        /// </summary>
        /// <param name="query">List query parameters including pagination, search, and filtering</param>
        /// <returns>Paginated list of assessments</returns>
        [HttpGet("List")]
        [ProducesResponseType(typeof(PaginatedResult<SingleAssessmentResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAssessmentList([FromQuery] ListQuery query)
        {
            var response = await Mediator.Send(query);
            return Ok(response);
        }

        /// <summary>
        /// Get assessment details by ID
        /// Accessible by Fund Manager, Legal Council, Board Secretary, Board Member (for assigned assessments)
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <returns>Assessment details</returns>
        [HttpGet("GetById")]
        [ProducesResponseType(typeof(BaseResponse<AssessmentByIdDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> GetAssessmentById(int id)
        {
            // Validate ID parameter
            if (id <= 0)
            {
                return BadRequest("Invalid assessment ID format");
            }

            var query = new GetAssessmentByIdQuery(id);
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

        /// <summary>
        /// Get comprehensive assessment details with complete hierarchy
        /// Includes assessment, questions, options, responses, answers, and answer options
        /// Accessible by Fund Manager, Legal Council, Board Secretary (for detailed analysis)
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <returns>Comprehensive assessment details</returns>
        [HttpGet("details")]
        [ProducesResponseType(typeof(BaseResponse<AssessmentDetailsDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> GetAssessmentDetails(int id)
        {
            // Validate ID parameter
            if (id <= 0)
            {
                return BadRequest("Invalid assessment ID format");
            }

            var query = new GetAssessmentDetailsQuery(id);
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

        /// <summary>
        /// Create a new assessment (draft or submit for approval)
        /// Accessible by Fund Manager only
        /// User Story 1: Create New Assessment
        /// </summary>
        /// <param name="command">Assessment creation data</param>
        /// <returns>Created assessment details</returns>
        [HttpPost("Create")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public async Task<IActionResult> CreateAssessment([FromBody] CreateAssessmentCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        /// <summary>
        /// Update assessment (only allowed for draft status)
        /// Accessible by Fund Manager only
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <param name="command">Assessment update data</param>
        /// <returns>Updated assessment details</returns>
        [HttpPut("Edit")]
        [ProducesResponseType(typeof(BaseResponse<AssessmentDetailsDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateAssessment([FromBody] UpdateAssessmentCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        /// <summary>
        /// Delete assessment (only allowed for draft status)
        /// Accessible by Fund Manager only
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <returns>Success response</returns>
        [HttpDelete("Delete")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> DeleteAssessment(int id)
        {
            // Validate ID parameter
            if (id <= 0)
            {
                return BadRequest("Invalid assessment ID format");
            }

            var response = await Mediator.Send(new DeleteAssessmentCommand { Id = id });
            return NewResult(response);
        }

        #endregion

        #region Workflow Operations

        /// <summary>
        /// Approve assessment for distribution
        /// Accessible by Legal Council and Board Secretary only
        /// User Story 2: Approve or Reject Assessment
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <returns>Success response</returns>
        [HttpPut("approve")]
        [Authorize(Roles = $"{RoleHelper.LegalCouncil},{RoleHelper.BoardSecretary}")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ApproveAssessment(int id)
        {
            var response = await Mediator.Send(new ApproveAssessmentCommand { Id = id });
            return NewResult(response);
        }

        /// <summary>
        /// Reject assessment with reason
        /// Accessible by Legal Council and Board Secretary only
        /// User Story 2: Approve or Reject Assessment
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <param name="command">Rejection data including reason</param>
        /// <returns>Success response</returns>
        [HttpPut("reject")]
        [Authorize(Roles = $"{RoleHelper.LegalCouncil},{RoleHelper.BoardSecretary}")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> RejectAssessment([FromBody] RejectAssessmentCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        /// <summary>
        /// Distribute approved assessment to board members
        /// Accessible by Fund Manager only
        /// User Story 3: Distribute Assessment
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <returns>Success response</returns>
        [HttpPut("distribute")]
        [Authorize(Roles = RoleHelper.FundManager)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> DistributeAssessment(int id)
        {
            var response = await Mediator.Send(new DistributeAssessmentCommand { Id = id });
            return NewResult(response);
        }

        #endregion

        #region Response Operations

        /// <summary>
        /// Submit response to assessment
        /// Accessible by Board Member only
        /// User Story 4: Respond to Assessment
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <param name="command">Response data</param>
        /// <returns>Success response</returns>
        [HttpPut("response")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> SubmitAssessmentResponse(SubmitAssessmentResponseCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        /// <summary>
        /// Get assessment response by assessment ID and user ID
        /// Accessible by Board Member (own response) and management roles (all responses)
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <param name="userId">User ID (optional, defaults to current user for Board Members)</param>
        /// <returns>Assessment response details</returns>
        [HttpGet("getAssessmentResponse")]
        [ProducesResponseType(typeof(BaseResponse<AssessmentResultsDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetAssessmentResponse([FromQuery]GetAssessmentResultsQuery model)
        {
            var response = await Mediator.Send(model);
            return NewResult(response);
        }

        /// <summary>
        /// Get fund permission information for the current user
        /// Returns user's permissions and role information within a specific fund context
        /// Accessible by all authenticated users
        /// </summary>
        /// <param name="fundId">Fund ID to check permissions for</param>
        /// <returns>Fund permission information</returns>
        [HttpGet("fundPermission")]
        [ProducesResponseType(typeof(BaseResponse<FundAssessmentPermissionDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> GetFundPermission(int fundId)
        {
            var query = new GetFundPermissionQuery { FundId = fundId };
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

        #endregion
    }
}
