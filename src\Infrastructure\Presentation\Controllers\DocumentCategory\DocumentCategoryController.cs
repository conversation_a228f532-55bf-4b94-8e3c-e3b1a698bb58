﻿using Abstraction.Base.Dto;
using Abstraction.Base.Response;
using Abstraction.Common.Wappers;
using Abstraction.Contract.Service;
using Infrastructure.Dto.DocumentCategory;
using Infrastructure.Dto.Strategies;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Presentation.Bases;

namespace Presentation.Controllers
{
    [Route("api/DocumentCategories/[action]")]
    [ApiController]
    public class DocumentCategoryController : BaseController
    {

        IDocumentCategoryService _documentCategoryService;
        public DocumentCategoryController(IDocumentCategoryService documentCategoryService)
        {
            _documentCategoryService = documentCategoryService;
        }

        [HttpPost]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public virtual async Task<IActionResult> CreateDocumentCategory([FromBody] DocumentCategoryAddDto entity)
        {
            var returnValue = await _documentCategoryService.AddAsync(entity);
            return NewResult(returnValue);
        }

        [HttpPut]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public virtual async Task<IActionResult> UpdateDocumentCategory([FromBody] DocumentCategoryEditDto entity)
        {
            var returnValue = await _documentCategoryService.UpdateAsync(entity);
            return NewResult(returnValue);
        }
        [HttpGet]
        [ProducesResponseType(typeof(BaseResponse<BaseDocumentCategoryDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetDocumentCategoryById(int id)
        {
            var returnValue = await _documentCategoryService.GetByIdAsync<BaseDocumentCategoryDto>(id, false);
            return NewResult(returnValue);
        }

        [HttpGet]
        [ProducesResponseType(typeof(PaginatedResult<BaseDocumentCategoryDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DocumentCategoryList([FromQuery] BaseListDto query)
        {
            var returnValue = await _documentCategoryService.GetAllPagedAsync<BaseDocumentCategoryDto>(query.PageNumber, query.PageSize, query.OrderBy, false);
            return NewResult(returnValue);
        }
    }
}
