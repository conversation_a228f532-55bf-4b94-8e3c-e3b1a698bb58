﻿using Microsoft.AspNetCore.Mvc;
using Application.Features.ResolutionMemberVotes.Commands.Edit;
using Application.Features.ResolutionMemberVotes.Queries.Get;
using Presentation.Bases;
using Abstraction.Base.Response;
using Microsoft.AspNetCore.Http;
using Application.Features.ResolutionMemberVotes.Dtos;
using DocumentFormat.OpenXml.Office2010.Excel;
using Application.Features.ResolutionMemberVotes.Queries.GetResolutionMembersByResult;
using Domain.Entities.ResolutionManagement.Enums;
using Application.Features.ResolutionMemberVotes.Commands.AddItemComment;
using Application.Features.ResolutionMemberVotes.Queries.GetResolutionItemVoteComments;
using Application.Features.ResolutionMemberVotes.Commands.ResendReminderNotification;
using Application.Features.ResolutionMemberVotes.Commands.RequestRevote;


namespace Presentation.Controllers.ResolutionVoting
{
    [Route("api/[controller]")]
    [ApiController]
    public class ResolutionMemberVoteController : AppControllerBase
    {
        [HttpGet("ViewMemberVote")]
        [ProducesResponseType(typeof(BaseResponse<ResolutionMemberVoteDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetResolutionMemberVotesById(int? id, int? memberID)
        {
            var response = await Mediator.Send(new GetQuery() { Id = id, MemberId = memberID });
            return NewResult(response);
        }

        [HttpPut("SubmitVote")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public async Task<IActionResult> EditResolutionMemberVote([FromBody] EditResolutionMemberVoteCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpPost("AddItemVoteComment")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public async Task<IActionResult> AddItemVoteComment([FromBody] ItemCommentCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpPost("AddMemberVoteComment")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        public async Task<IActionResult> AddMemberVoteComment([FromBody] MemberVoteCommentCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }
        /// <summary>
        /// Get all comments associated with a specific ResolutionItemVote
        /// Returns comments for the ResolutionItem that the ResolutionItemVote is voting on
        /// Includes user role metadata and localized role display names
        /// </summary>
        /// <param name="resolutionItemVoteId">The ID of the ResolutionItemVote to retrieve comments for</param>
        /// <returns>List of comment DTOs with user role information</returns>
        [HttpGet("ResolutionItemVoteComments")]
        [ProducesResponseType(typeof(BaseResponse<List<ResolutionItemVoteCommentDto>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<List<string>>), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> GetResolutionItemVoteComments(int id)
        {
            var response = await Mediator.Send(new GetResolutionItemVoteCommentsQuery { Id = id });
            return NewResult(response);
        }

        [HttpGet("ResolutionMembers")]
        [ProducesResponseType(typeof(BaseResponse<ResolutionMembersDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetResolutionMembers(int? id)
        {
            var response = await Mediator.Send(new GetResolutionMembersQuery() { Id = id});
            return NewResult(response);
        }
        [HttpGet("MemberVote")]
        [ProducesResponseType(typeof(BaseResponse<ResolutionMemberVoteResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetResolutionMemberVote(int? id , int? memberId )
        {
            var response = await Mediator.Send(new GetResolutionMemberVoteQuery() { Id = id.Value ,MemeberId= memberId });
            return NewResult(response);
        }

        [HttpGet("ResolutionItemMembersByVoteResult")]
        [ProducesResponseType(typeof(BaseResponse<List<string>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ResolutionItemMembersByVoteResult(int? id, ItemVoteResult itemVoteResult)
        {
            var response = await Mediator.Send(new GetResolutionMembersByResultQuery() { Id = id.Value, ItemVoteResult = itemVoteResult });
            return NewResult(response);
        }



        [HttpPost("SendReminder")]
        [ProducesResponseType(typeof(BaseResponse<List<string>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<List<string>>), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> SendReminder(int id ,int memberId)
        {
            var response = await Mediator.Send(new SendReminderNotificationCommand { ResolutionId = id, BoardMemberId = memberId});
            return NewResult(response);
        }

        [HttpPost("RequestReVote")]
        [ProducesResponseType(typeof(BaseResponse<List<string>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<List<string>>), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> RequestReVote(int id)
        {
            var response = await Mediator.Send(new RequestRevoteCommand { ResolutionMemberVoteId = id});
            return NewResult(response);
        }

        [HttpPost("ApproveRejectReVote")]
        [ProducesResponseType(typeof(BaseResponse<List<string>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<List<string>>), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> ApproveReVote(int id, bool isApproved)
        {
            var response = await Mediator.Send(new ApproveRevoteCommand { ResolutionMemberVoteId = id ,IsApproved = isApproved });
            return NewResult(response);
        }
    }
}
