using Xunit;
using Moq;
using AutoMapper;
using Microsoft.Extensions.Localization;
using Abstraction.Contracts.Repository;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Logger;
using Application.Features.AssessmentManagement.Queries.GetById;
using Application.Features.AssessmentManagement.Dtos;
using Application.Features.AssessmentManagement.Helpers;
using Domain.Entities.AssessmentManagement;
using Domain.Entities.AssessmentManagement.Enums;
using Domain.Entities.FundManagement;
using Domain.Entities.Shared;
using Abstraction.Constants;
using Resources;
using System.Net;

namespace Application.Tests.Features.AssessmentManagement.Queries.GetById
{
    /// <summary>
    /// Unit tests for GetAssessmentByIdQueryHandler
    /// Tests the CQRS query handler for retrieving assessment details by ID
    /// </summary>
    public class GetAssessmentByIdQueryHandlerTests
    {
        private readonly Mock<ILoggerManager> _mockLogger;
        private readonly Mock<IRepositoryManager> _mockRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IStringLocalizer<SharedResources>> _mockLocalizer;
        private readonly Mock<ICurrentUserService> _mockCurrentUserService;
        private readonly GetAssessmentByIdQueryHandler _handler;

        public GetAssessmentByIdQueryHandlerTests()
        {
            _mockLogger = new Mock<ILoggerManager>();
            _mockRepository = new Mock<IRepositoryManager>();
            _mockMapper = new Mock<IMapper>();
            _mockLocalizer = new Mock<IStringLocalizer<SharedResources>>();
            _mockCurrentUserService = new Mock<ICurrentUserService>();

            _handler = new GetAssessmentByIdQueryHandler(
                _mockLogger.Object,
                _mockRepository.Object,
                _mockMapper.Object,
                _mockLocalizer.Object,
                _mockCurrentUserService.Object);
        }

        [Fact]
        public async Task Handle_ValidRequest_ReturnsSuccessResponse()
        {
            // Arrange
            var assessmentId = 1;
            var userId = 123;
            var query = new GetAssessmentByIdQuery(assessmentId);

            var mockUser = new Domain.Entities.Users.User { Id = userId };
            var mockFund = new Fund
            {
                Id = 1,
                Name = "Test Fund",
                BoardMembers = new List<BoardMember>
                {
                    new BoardMember { UserId = userId, FundId = 1 }
                }
            };
            var mockAssessment = new Assessment
            {
                Id = assessmentId,
                Title = "Test Assessment",
                Description = "Test Description",
                Type = AssessmentType.Questionnaire,
                Status = AssessmentStatus.Active,
                FundId = 1,
                Fund = mockFund,
                CreatedBy = 999, // Different user created it
                Responses = new List<AssessmentResponse>(), // No existing response
                StatusHistories = new List<AssessmentStatusHistory>(),
                Questions = new List<AssessmentQuestion>
                {
                    new AssessmentQuestion
                    {
                        Id = 1,
                        QuestionText = "Test Question",
                        QuestionType = QuestionType.MultipleChoice,
                        DisplayOrder = 1,
                        IsRequired = true,
                        Options = new List<Option>
                        {
                            new Option { Id = 1, Value = "Option 1", Order = 1, IsCorrect = false },
                            new Option { Id = 2, Value = "Option 2", Order = 2, IsCorrect = true }
                        }
                    }
                }
            };

            var expectedDto = new AssessmentByIdDto
            {
                Id = assessmentId,
                Title = "Test Assessment",
                Description = "Test Description",
                Type = AssessmentType.Questionnaire,
                FundId = 1,
                FundName = "Test Fund",
                Questions = new List<AssessmentQuestionByIdDto>
                {
                    new AssessmentQuestionByIdDto
                    {
                        Id = 1,
                        QuestionText = "Test Question",
                        QuestionType = QuestionType.MultipleChoice,
                        DisplayOrder = 1,
                        IsRequired = true,
                        Options = new List<AssessmentOptionByIdDto>
                        {
                            new AssessmentOptionByIdDto { Id = 1, Value = "Option 1", Order = 1, IsCorrect = false },
                            new AssessmentOptionByIdDto { Id = 2, Value = "Option 2", Order = 2, IsCorrect = true }
                        }
                    }
                },
                StatusHistory = new List<AssessmentStatusHistoryByIdDto>(),
                // Expected permissions for Board Member with Active assessment and no existing response
                CanEdit = false,
                CanDelete = false,
                CanApprove = false,
                CanReject = false,
                CanDistribute = false,
                CanAnswer = true // Board Member can answer active assessment
            };

            _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
            _mockRepository.Setup(x => x.User.GetByIdAsync<Domain.Entities.Users.User>(userId, false))
                .ReturnsAsync(mockUser);
            _mockRepository.Setup(x => x.Assessment.GetAssessmentWithDetailsAsync(assessmentId, false))
                .ReturnsAsync(mockAssessment);
            _mockRepository.Setup(x => x.Funds.ViewFundUsers(1, false))
                .ReturnsAsync(mockFund);
            _mockMapper.Setup(x => x.Map<AssessmentByIdDto>(mockAssessment))
                .Returns(expectedDto);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Successed);
            Assert.Equal(HttpStatusCode.OK, result.StatusCode);
            Assert.NotNull(result.Data);
            Assert.Equal(assessmentId, result.Data.Id);
            Assert.Equal("Test Assessment", result.Data.Title);
            Assert.Equal("Test Fund", result.Data.FundName);
            Assert.Single(result.Data.Questions);
            Assert.Equal(2, result.Data.Questions.First().Options.Count);

            // Assert permission properties for Board Member with Active assessment
            Assert.False(result.Data.CanEdit);
            Assert.False(result.Data.CanDelete);
            Assert.False(result.Data.CanApprove);
            Assert.False(result.Data.CanReject);
            Assert.False(result.Data.CanDistribute);
            Assert.True(result.Data.CanAnswer); // Board Member can answer active assessment

            _mockLogger.Verify(x => x.LogInfo($"Getting assessment details for ID: {assessmentId}"), Times.Once);
            _mockLogger.Verify(x => x.LogInfo($"Successfully retrieved assessment details for ID: {assessmentId}"), Times.Once);
        }

        [Fact]
        public async Task Handle_UserNotAuthenticated_ReturnsUnauthorized()
        {
            // Arrange
            var query = new GetAssessmentByIdQuery(1);
            _mockCurrentUserService.Setup(x => x.UserId).Returns((int?)null);
            _mockLocalizer.Setup(x => x["UnauthorizedAccess"]).Returns(new LocalizedString("UnauthorizedAccess", "Unauthorized access"));

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Successed);
            Assert.Equal(HttpStatusCode.Unauthorized, result.StatusCode);
        }

        [Fact]
        public async Task Handle_AssessmentNotFound_ReturnsNotFound()
        {
            // Arrange
            var assessmentId = 999;
            var userId = 123;
            var query = new GetAssessmentByIdQuery(assessmentId);
            var mockUser = new Domain.Entities.Users.User { Id = userId };

            _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
            _mockRepository.Setup(x => x.User.GetByIdAsync<Domain.Entities.Users.User>(userId, false))
                .ReturnsAsync(mockUser);
            _mockRepository.Setup(x => x.Assessment.GetAssessmentWithDetailsAsync(assessmentId, false))
                .ReturnsAsync((Assessment?)null);
            _mockLocalizer.Setup(x => x["AssessmentNotFound"]).Returns(new LocalizedString("AssessmentNotFound", "Assessment not found"));

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Successed);
            Assert.Equal(HttpStatusCode.NotFound, result.StatusCode);
        }

        [Theory]
        [InlineData(AssessmentStatus.Draft, Roles.FundManager, true, true, true, false, false, false, false, false)] // Creator can edit/delete
        [InlineData(AssessmentStatus.Draft, Roles.FundManager, false, false, false, false, false, false, false, false)] // Non-creator cannot
        [InlineData(AssessmentStatus.WaitingForApproval, Roles.LegalCouncil, false, true, true, true, true, false, false, false)] // Legal Council can approve/reject/edit/delete
        [InlineData(AssessmentStatus.WaitingForApproval, Roles.BoardSecretary, false, true, true, true, true, false, false, false)] // Board Secretary can approve/reject/edit/delete
        [InlineData(AssessmentStatus.WaitingForApproval, Roles.FundManager, false, false, false, false, false, false, false, false)] // Fund Manager cannot
        [InlineData(AssessmentStatus.Rejected, Roles.LegalCouncil, false, true, true, false, false, false, false, false)] // Legal Council can edit/delete rejected
        [InlineData(AssessmentStatus.Rejected, Roles.BoardSecretary, false, true, true, false, false, false, false, false)] // Board Secretary can edit/delete rejected
        [InlineData(AssessmentStatus.Approved, Roles.FundManager, false, false, false, false, false, true, false, false)] // Fund Manager can distribute approved
        [InlineData(AssessmentStatus.Approved, Roles.LegalCouncil, false, false, false, false, false, false, false, false)] // Legal Council cannot distribute
        [InlineData(AssessmentStatus.Active, Roles.BoardMember, false, false, false, false, false, false, true, false)] // Board Member can answer active (no existing response)
        [InlineData(AssessmentStatus.Active, Roles.BoardMember, false, false, false, false, false, false, false, true)] // Board Member cannot answer (has existing response)
        [InlineData(AssessmentStatus.Completed, Roles.FundManager, false, false, false, false, false, false, false, false)] // All read-only when completed
        public async Task Handle_PermissionCalculation_ReturnsCorrectPermissions(
            AssessmentStatus status,
            Roles userRole,
            bool isCreator,
            bool expectedCanEdit,
            bool expectedCanDelete,
            bool expectedCanApprove,
            bool expectedCanReject,
            bool expectedCanDistribute,
            bool expectedCanAnswer,
            bool hasExistingResponse)
        {
            // Arrange
            var assessmentId = 1;
            var userId = 123;
            var creatorId = isCreator ? userId : 999;
            var query = new GetAssessmentByIdQuery(assessmentId);

            var mockUser = new Domain.Entities.Users.User { Id = userId };
            var mockFund = CreateMockFundWithRole(userId, userRole);
            var mockAssessment = CreateMockAssessment(assessmentId, status, creatorId, mockFund);

            if (hasExistingResponse)
            {
                mockAssessment.Responses = new List<AssessmentResponse>
                {
                    new AssessmentResponse { UserId = userId, AssessmentId = assessmentId }
                };
            }

            var expectedDto = new AssessmentByIdDto
            {
                Id = assessmentId,
                Title = "Test Assessment",
                FundId = 1,
                FundName = "Test Fund",
                StatusHistory = new List<AssessmentStatusHistoryByIdDto>()
            };

            _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
            _mockRepository.Setup(x => x.User.GetByIdAsync<Domain.Entities.Users.User>(userId, false))
                .ReturnsAsync(mockUser);
            _mockRepository.Setup(x => x.Assessment.GetAssessmentWithDetailsAsync(assessmentId, false))
                .ReturnsAsync(mockAssessment);
            _mockRepository.Setup(x => x.Funds.ViewFundUsers(1, false))
                .ReturnsAsync(mockFund);
            _mockMapper.Setup(x => x.Map<AssessmentByIdDto>(mockAssessment))
                .Returns(expectedDto);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Successed);
            Assert.Equal(expectedCanEdit, result.Data.CanEdit);
            Assert.Equal(expectedCanDelete, result.Data.CanDelete);
            Assert.Equal(expectedCanApprove, result.Data.CanApprove);
            Assert.Equal(expectedCanReject, result.Data.CanReject);
            Assert.Equal(expectedCanDistribute, result.Data.CanDistribute);
            Assert.Equal(expectedCanAnswer, result.Data.CanAnswer);
        }

        private Fund CreateMockFundWithRole(int userId, Roles role)
        {
            var fund = new Fund { Id = 1, Name = "Test Fund" };

            switch (role)
            {
                case Roles.FundManager:
                    fund.FundManagers = new List<FundManager> { new FundManager { UserId = userId, FundId = 1 } };
                    break;
                case Roles.LegalCouncil:
                    fund.LegalCouncilId = userId;
                    break;
                case Roles.BoardSecretary:
                    fund.FundBoardSecretaries = new List<FundBoardSecretary> { new FundBoardSecretary { UserId = userId, FundId = 1 } };
                    break;
                case Roles.BoardMember:
                    fund.BoardMembers = new List<BoardMember> { new BoardMember { UserId = userId, FundId = 1 } };
                    break;
            }

            return fund;
        }

        private Assessment CreateMockAssessment(int assessmentId, AssessmentStatus status, int creatorId, Fund fund)
        {
            return new Assessment
            {
                Id = assessmentId,
                Title = "Test Assessment",
                Status = status,
                FundId = 1,
                Fund = fund,
                CreatedBy = creatorId,
                Responses = new List<AssessmentResponse>(),
                StatusHistories = new List<AssessmentStatusHistory>(),
                Questions = new List<AssessmentQuestion>()
            };
        }
    }
}
