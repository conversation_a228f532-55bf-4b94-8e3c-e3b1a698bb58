using AutoMapper;
using Application.Mapping;
using Domain.Entities.ResolutionManagement;
using Application.Features.ResolutionMemberVotes.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Test
{
    /// <summary>
    /// Simple test to verify the AutoMapper configuration fix for Resolution -> ResolutionMemberVoteResponse mapping
    /// </summary>
    public class AutoMapperTest
    {
        public static void TestMapping()
        {
            // Configure AutoMapper
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<ResolutionMemberVotesProfile>();
                // Add other profiles as needed
            });

            var mapper = config.CreateMapper();

            // Create test data
            var resolution = new Resolution
            {
                Id = 1,
                Code = "TEST001/2024/001",
                ResolutionDate = DateTime.Now,
                Description = "Test Resolution",
                ResolutionTypeId = 1,
                VotingType = VotingType.AllMembers,
                Status = ResolutionStatusEnum.Draft,
                FundId = 1,
                ResolutionMemberVotes = new List<ResolutionMemberVote>
                {
                    new ResolutionMemberVote
                    {
                        Id = 1,
                        ResolutionId = 1,
                        BoardMemberID = 1,
                        VoteResult = VoteResult.Accept,
                        CreatedAt = DateTime.Now,
                        CreatedBy = 1,
                        ResolutionItemVotes = new List<ResolutionItemVote>(),
                        ResolutionMemberVoteComments = new List<ResolutionMemberVoteComment>()
                    }
                }
            };

            try
            {
                // Test the mapping
                var result = mapper.Map<ResolutionMemberVoteResponse>(resolution);
                
                Console.WriteLine("✅ AutoMapper configuration test PASSED!");
                Console.WriteLine($"Resolution ID: {result.Id}");
                Console.WriteLine($"Resolution Code: {result.Code}");
                Console.WriteLine($"ResolutionMemberVote mapped: {result.ResolutionMemberVote != null}");
                
                if (result.ResolutionMemberVote != null)
                {
                    Console.WriteLine($"ResolutionMemberVote ID: {result.ResolutionMemberVote.Id}");
                    Console.WriteLine($"ResolutionMemberVote ResolutionId: {result.ResolutionMemberVote.ResolutionId}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("❌ AutoMapper configuration test FAILED!");
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
            }
        }
    }
}
